const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkOutWarranties() {
  try {
    console.log('🔍 Checking out-warranty records in database...\n');
    
    // Check total count
    const totalCount = await prisma.out_warranties.count();
    console.log(`📊 Total out-warranty records: ${totalCount}`);
    
    // Check active records
    const activeCount = await prisma.out_warranties.count({
      where: { isActive: true }
    });
    console.log(`✅ Active out-warranty records: ${activeCount}`);
    
    // Check inactive records
    const inactiveCount = await prisma.out_warranties.count({
      where: { isActive: false }
    });
    console.log(`❌ Inactive out-warranty records: ${inactiveCount}`);
    
    // Get sample records
    const sampleRecords = await prisma.out_warranties.findMany({
      take: 5,
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            city: true,
          }
        },
        executive: {
          select: {
            id: true,
            name: true,
          }
        }
      }
    });
    
    console.log('\n📋 Sample records:');
    sampleRecords.forEach((record, index) => {
      console.log(`${index + 1}. ID: ${record.id}`);
      console.log(`   Customer: ${record.customer?.name || 'N/A'}`);
      console.log(`   Executive: ${record.executive?.name || 'N/A'}`);
      console.log(`   Start Date: ${record.startDate}`);
      console.log(`   End Date: ${record.endDate}`);
      console.log(`   Amount: ${record.amount}`);
      console.log(`   Is Active: ${record.isActive}`);
      console.log(`   Source: ${record.source}`);
      console.log('   ---');
    });
    
    // Check related tables
    const machinesCount = await prisma.out_warranty_machines.count();
    console.log(`\n🔧 Out-warranty machines: ${machinesCount}`);
    
    const paymentsCount = await prisma.out_warranty_payments.count();
    console.log(`💰 Out-warranty payments: ${paymentsCount}`);
    
  } catch (error) {
    console.error('❌ Error checking out-warranties:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkOutWarranties();
