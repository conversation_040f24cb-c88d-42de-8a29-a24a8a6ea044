import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { ConversionReportRepository } from '@/lib/repositories/conversion-report.repository';
import { conversionReportFilterSchema } from '@/lib/validations/conversion.schema';
import { logActivity } from '@/lib/activity-logger';

/**
 * GET /api/conversions/reports/details
 * Get detailed conversion records with filtering and pagination
 */
async function detailsHandler(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Parse and validate filters
    const filters = {
      dateFrom: searchParams.get('dateFrom') ? new Date(searchParams.get('dateFrom')!) : undefined,
      dateTo: searchParams.get('dateTo') ? new Date(searchParams.get('dateTo')!) : undefined,
      conversionType: searchParams.get('conversionType') || undefined,
      customerId: searchParams.get('customerId') || undefined,
      status: searchParams.get('status') || undefined,
      userId: searchParams.get('userId') || undefined,
      amountMin: searchParams.get('amountMin') ? parseFloat(searchParams.get('amountMin')!) : undefined,
      amountMax: searchParams.get('amountMax') ? parseFloat(searchParams.get('amountMax')!) : undefined,
      page: searchParams.get('page') ? parseInt(searchParams.get('page')!) : 1,
      limit: searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : 20,
      orderBy: (searchParams.get('orderBy') || 'createdAt') as 'createdAt' | 'effectiveDate' | 'amount',
      orderDirection: (searchParams.get('orderDirection') || 'desc') as 'asc' | 'desc',
    };

    const validatedFilters = conversionReportFilterSchema.parse(filters);
    
    // Get detailed conversions
    const repository = new ConversionReportRepository();
    const result = await repository.getDetailedConversions(validatedFilters);

    // Log the activity
    await logActivity({
      action: 'VIEW',
      entityType: 'CONVERSION_REPORT',
      details: `Viewed detailed conversion records (page ${validatedFilters.page})`,
      request
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error fetching detailed conversions:', error);
    return NextResponse.json(
      { error: 'Failed to fetch detailed conversions' },
      { status: 500 }
    );
  }
}

export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  detailsHandler
);
