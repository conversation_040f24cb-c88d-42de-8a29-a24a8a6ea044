import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { getSalesProspectRepository } from '@/lib/repositories';
import { updateSalesProspectSchema } from '@/lib/validations/sales.schema';
import { z } from 'zod';

/**
 * GET /api/sales/prospects/[id]
 * Get a specific sales prospect by ID
 */
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  async (
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      const { id } = await params;

      const salesProspectRepository = getSalesProspectRepository();
      const salesProspect = await salesProspectRepository.findById(id);

      if (!salesProspect) {
        return NextResponse.json(
          {
            success: false,
            error: 'Sales prospect not found',
          },
          { status: 404 }
        );
      }

      return NextResponse.json({
        success: true,
        data: salesProspect,
      });
    } catch (error) {
      console.error('Error fetching sales prospect:', error);
      
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to fetch sales prospect',
        },
        { status: 500 }
      );
    }
  }
);

/**
 * PUT /api/sales/prospects/[id]
 * Update a specific sales prospect
 */
export const PUT = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  async (
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      const { id } = await params;
      const body = await request.json();

      // Validate request body
      const validatedData = updateSalesProspectSchema.parse({ ...body, id });

      const salesProspectRepository = getSalesProspectRepository();

      // Check if sales prospect exists
      const existingSalesProspect = await salesProspectRepository.findById(id);
      if (!existingSalesProspect) {
        return NextResponse.json(
          {
            success: false,
            error: 'Sales prospect not found',
          },
          { status: 404 }
        );
      }

      // Update sales prospect
      const updateData: any = {};
      
      if (validatedData.customerId) updateData.customerId = validatedData.customerId;
      if (validatedData.executiveId) updateData.executiveId = validatedData.executiveId;
      if (validatedData.prospectDate) updateData.prospectDate = validatedData.prospectDate;
      if (validatedData.contactPerson !== undefined) updateData.contactPerson = validatedData.contactPerson;
      if (validatedData.contactPhone !== undefined) updateData.contactPhone = validatedData.contactPhone;
      if (validatedData.status) updateData.status = validatedData.status;
      if (validatedData.prospectPercentage !== undefined) updateData.prospectPercentage = validatedData.prospectPercentage;
      if (validatedData.followUpDate !== undefined) updateData.followUpDate = validatedData.followUpDate;
      if (validatedData.nextVisitDate !== undefined) updateData.nextVisitDate = validatedData.nextVisitDate;
      if (validatedData.lostReason !== undefined) updateData.lostReason = validatedData.lostReason;
      if (validatedData.remarks !== undefined) updateData.remarks = validatedData.remarks;

      const updatedSalesProspect = await salesProspectRepository.update(id, updateData);

      return NextResponse.json({
        success: true,
        data: updatedSalesProspect,
        message: 'Sales prospect updated successfully',
      });
    } catch (error) {
      console.error('Error updating sales prospect:', error);
      
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid request data',
            details: error.errors,
          },
          { status: 400 }
        );
      }
      
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to update sales prospect',
        },
        { status: 500 }
      );
    }
  }
);

/**
 * DELETE /api/sales/prospects/[id]
 * Delete a specific sales prospect
 */
export const DELETE = withRoleProtection(
  ['ADMIN', 'MANAGER'],
  async (
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      const { id } = await params;

      const salesProspectRepository = getSalesProspectRepository();

      // Check if sales prospect exists
      const existingSalesProspect = await salesProspectRepository.findById(id);
      if (!existingSalesProspect) {
        return NextResponse.json(
          {
            success: false,
            error: 'Sales prospect not found',
          },
          { status: 404 }
        );
      }

      // Delete sales prospect
      await salesProspectRepository.delete(id);

      return NextResponse.json({
        success: true,
        message: 'Sales prospect deleted successfully',
      });
    } catch (error) {
      console.error('Error deleting sales prospect:', error);
      
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to delete sales prospect',
        },
        { status: 500 }
      );
    }
  }
);
