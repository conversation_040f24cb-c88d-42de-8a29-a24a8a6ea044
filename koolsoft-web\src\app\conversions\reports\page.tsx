'use client';

import React, { useState } from 'react';
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowRightLeft, BarChart3, FileText, Download } from 'lucide-react';
import { ConversionStatisticsWidget } from '@/components/conversions/reports/conversion-statistics-widget';
import { ConversionTrendsChart } from '@/components/conversions/reports/conversion-trends-chart';
import { ConversionDetailsTable } from '@/components/conversions/reports/conversion-details-table';
import { ConversionFilters } from '@/components/conversions/reports/conversion-filters';
import { ConversionReportFilter } from '@/lib/validations/conversion.schema';
import { toast } from 'sonner';

/**
 * Conversion Reports Page
 * 
 * Main interface for conversion reporting and analytics
 */
export default function ConversionReportsPage() {
  const [filters, setFilters] = useState<ConversionReportFilter>({
    page: 1,
    limit: 20,
    orderBy: 'createdAt',
    orderDirection: 'desc',
  });
  const [isExporting, setIsExporting] = useState(false);

  const handleFiltersChange = (newFilters: Partial<ConversionReportFilter>) => {
    setFilters(prev => ({
      ...prev,
      ...newFilters,
      page: 1, // Reset to first page when filters change
    }));
  };

  const handleExport = async (format: 'CSV' | 'EXCEL' | 'PDF') => {
    try {
      setIsExporting(true);
      
      const response = await fetch('/api/conversions/reports/export', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          format,
          filters,
          includeDetails: true,
          includeStatistics: false,
        }),
      });

      if (!response.ok) {
        throw new Error('Export failed');
      }

      // Get filename from response headers
      const contentDisposition = response.headers.get('Content-Disposition');
      const filename = contentDisposition?.split('filename=')[1]?.replace(/"/g, '') || 
                     `conversion_reports.${format.toLowerCase()}`;

      // Create download link
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast.success(`Conversion reports exported as ${format}`);
    } catch (error) {
      console.error('Export error:', error);
      toast.error('Failed to export conversion reports');
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Conversion Reports</h1>
          <p className="text-gray-600 mt-1">
            Analyze conversion patterns, success rates, and business metrics
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            onClick={() => handleExport('CSV')}
            disabled={isExporting}
            variant="outline"
          >
            <Download className="h-4 w-4 mr-2" />
            Export CSV
          </Button>
          <Button
            onClick={() => handleExport('EXCEL')}
            disabled={isExporting}
            variant="outline"
          >
            <Download className="h-4 w-4 mr-2" />
            Export Excel
          </Button>
          <Button
            onClick={() => handleExport('PDF')}
            disabled={isExporting}
            variant="outline"
          >
            <Download className="h-4 w-4 mr-2" />
            Export PDF
          </Button>
        </div>
      </div>

      {/* Filters */}
      <ConversionFilters
        filters={filters}
        onFiltersChange={handleFiltersChange}
      />

      {/* Main Content */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="statistics" className="flex items-center gap-2">
            <ArrowRightLeft className="h-4 w-4" />
            Statistics
          </TabsTrigger>
          <TabsTrigger value="trends" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Trends
          </TabsTrigger>
          <TabsTrigger value="details" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Details
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Statistics Widget */}
          <ConversionStatisticsWidget />
          
          {/* Trends Chart */}
          <ConversionTrendsChart defaultPeriod="monthly" />
        </TabsContent>

        <TabsContent value="statistics" className="space-y-6">
          <ConversionStatisticsWidget showTitle={false} />
          
          {/* Additional Statistics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardHeader className="bg-primary text-white">
                <CardTitle className="flex items-center gap-2">
                  <ArrowRightLeft className="h-5 w-5" />
                  Conversion Types
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Warranty → AMC</span>
                    <span className="text-lg font-bold text-green-600">Most Popular</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">AMC → Out-Warranty</span>
                    <span className="text-lg font-bold text-yellow-600">Moderate</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Warranty → Out-Warranty</span>
                    <span className="text-lg font-bold text-red-600">Least Common</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="bg-primary text-white">
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Performance Metrics
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="space-y-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">100%</div>
                    <div className="text-sm text-muted-foreground">Success Rate</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-primary">24h</div>
                    <div className="text-sm text-muted-foreground">Avg. Processing Time</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="bg-primary text-white">
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Quick Actions
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="space-y-3">
                  <Button 
                    className="w-full" 
                    variant="outline"
                    onClick={() => handleExport('CSV')}
                    disabled={isExporting}
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Export All Data
                  </Button>
                  <Button 
                    className="w-full" 
                    variant="outline"
                    onClick={() => window.location.href = '/conversions'}
                  >
                    <ArrowRightLeft className="h-4 w-4 mr-2" />
                    View Conversions
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="trends" className="space-y-6">
          <ConversionTrendsChart 
            showTitle={false} 
            defaultPeriod="monthly"
            chartType="line"
          />
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <ConversionTrendsChart 
              showTitle={true} 
              defaultPeriod="weekly"
              chartType="bar"
              className="lg:col-span-1"
            />
            <ConversionTrendsChart 
              showTitle={true} 
              defaultPeriod="quarterly"
              chartType="line"
              className="lg:col-span-1"
            />
          </div>
        </TabsContent>

        <TabsContent value="details" className="space-y-6">
          <ConversionDetailsTable 
            filters={filters}
            onFiltersChange={handleFiltersChange}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}
