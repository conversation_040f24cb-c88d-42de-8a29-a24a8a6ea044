{"/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/api/sales/leads/route": "app/api/sales/leads/route.js", "/api/sales/leads/[id]/route": "app/api/sales/leads/[id]/route.js", "/api/customers/route": "app/api/customers/route.js", "/api/users/route": "app/api/users/route.js", "/leads/page": "app/leads/page.js", "/leads/new/page": "app/leads/new/page.js", "/leads/[id]/page": "app/leads/[id]/page.js", "/leads/[id]/edit/page": "app/leads/[id]/edit/page.js"}