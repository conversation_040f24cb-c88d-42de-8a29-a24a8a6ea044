import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { getSalesLeadRepository } from '@/lib/repositories';
import { z } from 'zod';
import { format } from 'date-fns';

// Export validation schema
const leadExportSchema = z.object({
  format: z.enum(['CSV', 'EXCEL']).default('CSV'),
  customerId: z.string().uuid().optional(),
  executiveId: z.string().uuid().optional(),
  status: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  search: z.string().optional(),
});

/**
 * Generate CSV export for leads
 */
function generateCSVExport(leads: any[]): NextResponse {
  // CSV headers
  const headers = [
    'Lead ID',
    'Customer Name',
    'Customer City',
    'Customer Phone',
    'Customer Email',
    'Executive Name',
    'Executive Designation',
    'Lead Date',
    'Contact Person',
    'Contact Phone',
    'Status',
    'Prospect Percentage',
    'Follow Up Date',
    'Next Visit Date',
    'Remarks',
    'Created At',
    'Updated At'
  ];

  // Convert leads to CSV rows
  const csvRows = leads.map(lead => [
    lead.id,
    lead.customer?.name || '',
    lead.customer?.city || '',
    lead.customer?.phone || '',
    lead.customer?.email || '',
    lead.executive?.name || '',
    lead.executive?.designation || '',
    format(new Date(lead.leadDate), 'yyyy-MM-dd'),
    lead.contactPerson || '',
    lead.contactPhone || '',
    lead.status,
    lead.prospectPercentage || '',
    lead.followUpDate ? format(new Date(lead.followUpDate), 'yyyy-MM-dd') : '',
    lead.nextVisitDate ? format(new Date(lead.nextVisitDate), 'yyyy-MM-dd') : '',
    lead.remarks || '',
    format(new Date(lead.createdAt), 'yyyy-MM-dd HH:mm:ss'),
    format(new Date(lead.updatedAt), 'yyyy-MM-dd HH:mm:ss')
  ]);

  // Escape CSV values and join
  const escapeCsvValue = (value: any) => {
    const str = String(value || '');
    if (str.includes(',') || str.includes('"') || str.includes('\n')) {
      return `"${str.replace(/"/g, '""')}"`;
    }
    return str;
  };

  const csvHeader = headers.map(escapeCsvValue).join(',');
  const csvData = csvRows.map(row => row.map(escapeCsvValue).join(',')).join('\n');
  const csv = `${csvHeader}\n${csvData}`;

  // Generate filename
  const now = format(new Date(), 'yyyy-MM-dd_HHmmss');
  const filename = `leads_export_${now}.csv`;

  return new NextResponse(csv, {
    headers: {
      'Content-Type': 'text/csv',
      'Content-Disposition': `attachment; filename="${filename}"`,
    },
  });
}

/**
 * GET /api/sales/leads/export
 * Export leads data in various formats
 */
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  async (request: NextRequest) => {
    try {
      const { searchParams } = new URL(request.url);
      
      // Parse and validate query parameters
      const params = Object.fromEntries(searchParams.entries());
      const validatedParams = leadExportSchema.parse(params);

      const salesLeadRepository = getSalesLeadRepository();

      // Build filter object
      const filters: any = {
        skip: 0,
        take: 10000, // Large number to get all leads for export
        sortBy: 'leadDate',
        sortOrder: 'desc' as const,
      };

      if (validatedParams.customerId) {
        filters.customerId = validatedParams.customerId;
      }

      if (validatedParams.executiveId) {
        filters.executiveId = validatedParams.executiveId;
      }

      if (validatedParams.status) {
        filters.status = validatedParams.status;
      }

      if (validatedParams.startDate) {
        filters.startDate = new Date(validatedParams.startDate);
      }

      if (validatedParams.endDate) {
        filters.endDate = new Date(validatedParams.endDate);
      }

      if (validatedParams.search) {
        filters.search = validatedParams.search;
      }

      // Get leads for export
      const result = await salesLeadRepository.findWithFilters(filters);
      const leads = result.data;

      // Generate export based on format
      switch (validatedParams.format) {
        case 'CSV':
          return generateCSVExport(leads);
        case 'EXCEL':
          // For now, return CSV format for Excel as well
          // In the future, this could be enhanced to generate actual Excel files
          return generateCSVExport(leads);
        default:
          return NextResponse.json(
            { error: 'Unsupported export format' },
            { status: 400 }
          );
      }
    } catch (error) {
      console.error('Error exporting leads:', error);
      
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid export parameters',
            details: error.errors,
          },
          { status: 400 }
        );
      }
      
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to export leads',
        },
        { status: 500 }
      );
    }
  }
);
