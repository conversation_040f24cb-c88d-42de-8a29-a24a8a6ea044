'use client';

import { useState, use } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/components/ui/use-toast';
import { HistoryCardDetail, HistoryCardForm } from '@/components/history-cards';
import { 
  ArrowLeft, 
  Edit, 
  Trash2, 
  AlertTriangle,
  Loader2,
} from 'lucide-react';

interface HistoryCard {
  id: string;
  cardNo?: number;
  source?: 'AMC' | 'INW' | 'OTW';
  customerId: string;
  amcId?: string;
  inWarrantyId?: string;
  outWarrantyId?: string;
  toCardNo?: number;
  originalId?: number;
  createdAt: string;
  updatedAt: string;
  customer?: {
    id: string;
    name: string;
    address?: string;
    city?: string;
    state?: string;
    phone?: string;
    email?: string;
  };
  sections?: {
    id: string;
    sectionCode: string;
    content: string;
    createdAt: string;
    updatedAt: string;
  }[];
}

interface HistoryCardPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default function HistoryCardPage({ params }: HistoryCardPageProps) {
  const resolvedParams = use(params);
  const router = useRouter();
  const { toast } = useToast();
  
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [historyCard, setHistoryCard] = useState<HistoryCard | null>(null);
  const [refreshKey, setRefreshKey] = useState(0);

  const handleEdit = (card: HistoryCard) => {
    setHistoryCard(card);
    setShowEditDialog(true);
  };

  const handleDelete = (cardId: string) => {
    setShowDeleteDialog(true);
  };

  const handleConfirmDelete = async () => {
    try {
      setIsDeleting(true);
      
      const response = await fetch(`/api/history-cards/${resolvedParams.id}`, {
        method: 'DELETE',
        credentials: 'include',
      });
      
      if (!response.ok) {
        throw new Error('Failed to delete history card');
      }
      
      toast({
        title: 'Success',
        description: 'History card deleted successfully.',
      });
      
      // Navigate back to the list
      router.push('/history-cards');
    } catch (error) {
      console.error('Error deleting history card:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete history card. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsDeleting(false);
      setShowDeleteDialog(false);
    }
  };

  const handleFormSuccess = (updatedCard: HistoryCard) => {
    setShowEditDialog(false);
    setHistoryCard(updatedCard);
    setRefreshKey(prev => prev + 1);
    
    toast({
      title: 'Success',
      description: 'History card updated successfully.',
    });
  };

  const handleFormCancel = () => {
    setShowEditDialog(false);
  };

  const handleBack = () => {
    router.push('/history-cards');
  };

  return (
    <div className="space-y-6">
      {/* Back Button */}
      <Card>
        <CardContent className="py-4">
          <Button variant="outline" onClick={handleBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to History Cards
          </Button>
        </CardContent>
      </Card>

      {/* History Card Detail */}
      <HistoryCardDetail
        key={refreshKey}
        historyCardId={resolvedParams.id}
        onEdit={handleEdit}
        onDelete={handleDelete}
        showActions={true}
      />

      {/* Edit Dialog */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit History Card</DialogTitle>
          </DialogHeader>
          <HistoryCardForm
            historyCard={historyCard || undefined}
            onSuccess={handleFormSuccess}
            onCancel={handleFormCancel}
          />
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-red-500" />
              <span>Confirm Deletion</span>
            </DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this history card? This action cannot be undone 
              and will also delete all associated history sections.
            </DialogDescription>
          </DialogHeader>
          
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              This will permanently delete the history card and all its sections.
            </AlertDescription>
          </Alert>
          
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowDeleteDialog(false)}
              disabled={isDeleting}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleConfirmDelete}
              disabled={isDeleting}
            >
              {isDeleting ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Trash2 className="h-4 w-4 mr-2" />
              )}
              Delete History Card
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
