'use client';

import React from 'react';
import { usePathname } from 'next/navigation';
import { DashboardLayout } from '@/components/layout';
import { BreadcrumbItemType } from '@/components/layout/page-header';
import { Shield } from 'lucide-react';

/**
 * Warranty Layout Component
 *
 * This component provides a consistent layout for all warranty-related pages
 * using the standardized DashboardLayout component with collapsible sidebar.
 */
export default function WarrantyLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();

  // Determine the current page title and breadcrumbs based on the pathname
  let pageTitle = 'Warranty Management';
  let breadcrumbs: BreadcrumbItemType[] = [
    { label: 'Dashboard', href: '/' },
    { label: 'Warranty Management', href: '/warranties', icon: <Shield className="h-4 w-4" /> }
  ];

  // Update breadcrumbs and title based on current path
  if (pathname === '/warranties/in-warranty') {
    pageTitle = 'In-Warranty Management';
    breadcrumbs.push({ label: 'In-Warranty', current: true });
  } else if (pathname === '/warranties/out-warranty') {
    pageTitle = 'Out-of-Warranty Management';
    breadcrumbs.push({ label: 'Out-of-Warranty', current: true });
  } else if (pathname === '/warranties/components') {
    pageTitle = 'Component Tracking';
    breadcrumbs.push({ label: 'Components', current: true });
  } else if (pathname === '/warranties/status') {
    pageTitle = 'Warranty Status Dashboard';
    breadcrumbs.push({ label: 'Status Dashboard', current: true });
  } else if (pathname === '/warranties/alerts') {
    pageTitle = 'Warranty Alerts';
    breadcrumbs.push({ label: 'Alerts', current: true });
  } else if (pathname === '/warranties/bluestar') {
    pageTitle = 'BLUESTAR Warranties';
    breadcrumbs.push({ label: 'BLUESTAR', current: true });
  } else if (pathname === '/warranties/conversions') {
    pageTitle = 'Warranty Conversions';
    breadcrumbs.push({ label: 'Conversions', current: true });
  } else if (pathname.includes('/warranties/') && pathname.includes('/edit')) {
    pageTitle = 'Edit Warranty';
    breadcrumbs.push({ label: 'Edit', current: true });
  } else if (pathname.includes('/warranties/') && !pathname.includes('/new')) {
    pageTitle = 'Warranty Details';
    breadcrumbs.push({ label: 'Details', current: true });
  } else if (pathname === '/warranties/new') {
    pageTitle = 'New Warranty';
    breadcrumbs.push({ label: 'New Warranty', current: true });
  } else if (pathname === '/warranties') {
    pageTitle = 'Warranty Management';
    breadcrumbs[breadcrumbs.length - 1].current = true;
  }



  return (
    <DashboardLayout
      title={pageTitle}
      requireAuth={true}
      allowedRoles={['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER']}
      breadcrumbs={breadcrumbs}
    >
      {children}
    </DashboardLayout>
  );
}
