import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';

/**
 * GET /api/conversions/types
 * Get available conversion types and their descriptions
 */
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  async (request: NextRequest) => {
    try {
      const conversionTypes = [
        {
          type: 'WARRANTY_TO_AMC',
          name: 'Warranty to AMC',
          description: 'Convert an in-warranty record to an Annual Maintenance Contract',
          sourceModule: 'Warranty',
          targetModule: 'AMC',
          requiredFields: [
            'startDate',
            'endDate', 
            'amount',
            'numberOfServices',
            'natureOfService'
          ],
          optionalFields: [
            'executiveId',
            'contactPersonId'
          ]
        },
        {
          type: 'AMC_TO_OUT_WARRANTY',
          name: 'AMC to Out-of-Warranty',
          description: 'Convert an AMC contract to an out-of-warranty service record',
          sourceModule: 'AMC',
          targetModule: 'Out-of-Warranty',
          requiredFields: [
            'startDate',
            'endDate'
          ],
          optionalFields: [
            'executiveId',
            'contactPersonId',
            'technicianId'
          ]
        },
        {
          type: 'WARRANTY_TO_OUT_WARRANTY',
          name: 'Warranty to Out-of-Warranty',
          description: 'Convert an in-warranty record directly to an out-of-warranty service record',
          sourceModule: 'Warranty',
          targetModule: 'Out-of-Warranty',
          requiredFields: [
            'startDate',
            'endDate'
          ],
          optionalFields: [
            'executiveId',
            'contactPersonId',
            'technicianId'
          ]
        }
      ];

      return NextResponse.json({
        conversionTypes,
        totalTypes: conversionTypes.length,
      });

    } catch (error) {
      console.error('Error fetching conversion types:', error);
      return NextResponse.json(
        { error: 'Failed to fetch conversion types' },
        { status: 500 }
      );
    }
  }
);
