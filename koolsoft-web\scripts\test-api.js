const { getOutWarrantyRepository } = require('../src/lib/repositories');

async function testAPI() {
  try {
    console.log('🧪 Testing Out-Warranty API logic...\n');
    
    const outWarrantyRepository = getOutWarrantyRepository();
    
    // Test 1: Basic findAll
    console.log('📋 Test 1: Basic findAll()');
    const allRecords = await outWarrantyRepository.findAll();
    console.log(`Found ${allRecords.length} records with findAll()`);
    
    // Test 2: findWithRelations with no filter
    console.log('\n📋 Test 2: findWithRelations() with no filter');
    try {
      const noFilterRecords = await outWarrantyRepository.findWithRelations({});
      console.log(`Found ${noFilterRecords.length} records with no filter`);
    } catch (error) {
      console.error('❌ Error with no filter:', error.message);
    }
    
    // Test 3: findWithRelations with isActive: true
    console.log('\n📋 Test 3: findWithRelations() with isActive: true');
    try {
      const activeRecords = await outWarrantyRepository.findWithRelations({ isActive: true });
      console.log(`Found ${activeRecords.length} records with isActive: true`);
      if (activeRecords.length > 0) {
        console.log('Sample record structure:');
        console.log(JSON.stringify(activeRecords[0], null, 2));
      }
    } catch (error) {
      console.error('❌ Error with isActive: true:', error.message);
    }
    
    // Test 4: findWithRelations with isActive: false
    console.log('\n📋 Test 4: findWithRelations() with isActive: false');
    try {
      const inactiveRecords = await outWarrantyRepository.findWithRelations({ isActive: false });
      console.log(`Found ${inactiveRecords.length} records with isActive: false`);
    } catch (error) {
      console.error('❌ Error with isActive: false:', error.message);
    }
    
    // Test 5: Test the exact API call that the frontend makes
    console.log('\n📋 Test 5: Simulating frontend API call');
    try {
      const frontendRecords = await outWarrantyRepository.findWithRelations(
        {}, // No filter (like the frontend)
        0,  // skip
        50, // take
        { startDate: 'desc' } // orderBy
      );
      console.log(`Found ${frontendRecords.length} records with frontend simulation`);
    } catch (error) {
      console.error('❌ Error with frontend simulation:', error.message);
    }
    
  } catch (error) {
    console.error('❌ Error testing API:', error);
  }
}

testAPI();
