'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { toast } from '@/components/ui/use-toast';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarIcon, Loader2 } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';

// Validation schema for warranty to AMC conversion
const warrantyToAmcConversionSchema = z.object({
  reason: z.string().min(1, 'Conversion reason is required').max(500),
  effectiveDate: z.date({ required_error: 'Effective date is required' }),
  notes: z.string().max(1000).optional(),
  amcData: z.object({
    startDate: z.date({ required_error: 'AMC start date is required' }),
    endDate: z.date({ required_error: 'AMC end date is required' }),
    amount: z.number({
      required_error: 'AMC amount is required',
      invalid_type_error: 'AMC amount must be a number'
    }).positive('AMC amount must be greater than 0'),
    numberOfServices: z.number({
      invalid_type_error: 'Number of services must be a number'
    }).int().positive().default(4),
    natureOfService: z.string().min(1, 'Nature of service is required'),
    executiveId: z.string().uuid().optional(),
    contactPersonId: z.string().uuid().optional(),
  }).refine(
    (data) => data.endDate > data.startDate,
    {
      message: 'AMC end date must be after start date',
      path: ['endDate'],
    }
  ),
}).refine(
  (data) => data.amcData.startDate >= data.effectiveDate,
  {
    message: 'AMC start date must be on or after effective date',
    path: ['amcData', 'startDate'],
  }
);

type WarrantyToAmcConversionForm = z.infer<typeof warrantyToAmcConversionSchema>;

interface WarrantyToAmcConversionDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  warrantyId: string;
  warrantyDetails?: {
    id: string;
    customerId: string;
    customerName: string;
    bslNo?: string;
    numberOfMachines: number;
    executiveId?: string;
    contactPersonId?: string;
  };
  onSuccess?: () => void;
}

export function WarrantyToAmcConversionDialog({
  open,
  onOpenChange,
  warrantyId,
  warrantyDetails,
  onSuccess,
}: WarrantyToAmcConversionDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<WarrantyToAmcConversionForm>({
    resolver: zodResolver(warrantyToAmcConversionSchema),
    defaultValues: {
      reason: '',
      effectiveDate: new Date(),
      notes: '',
      amcData: {
        startDate: new Date(),
        endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
        amount: 0,
        numberOfServices: 4,
        natureOfService: '',
        executiveId: warrantyDetails?.executiveId || undefined,
        contactPersonId: warrantyDetails?.contactPersonId || undefined,
      },
    },
  });

  const onSubmit = async (data: WarrantyToAmcConversionForm) => {
    setIsSubmitting(true);
    try {
      console.log('WarrantyToAmcConversionDialog: Starting form submission', {
        warrantyId,
        formData: data
      });

      const response = await fetch('/api/conversions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          sourceId: warrantyId,
          conversionType: 'WARRANTY_TO_AMC',
          reason: data.reason,
          effectiveDate: data.effectiveDate.toISOString(),
          notes: data.notes,
          amcData: {
            startDate: data.amcData.startDate.toISOString(),
            endDate: data.amcData.endDate.toISOString(),
            amount: data.amcData.amount,
            numberOfServices: data.amcData.numberOfServices,
            natureOfService: data.amcData.natureOfService,
            executiveId: data.amcData.executiveId,
            contactPersonId: data.amcData.contactPersonId,
          },
        }),
      });

      console.log('WarrantyToAmcConversionDialog: API response received', {
        status: response.status,
        ok: response.ok
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to convert warranty to AMC');
      }

      const result = await response.json();
      
      toast({
        title: 'Conversion Successful',
        description: `Warranty has been successfully converted to AMC contract ${result.targetId}`,
      });

      onOpenChange(false);
      form.reset();
      onSuccess?.();
    } catch (error) {
      console.error('Error converting warranty to AMC:', error);
      toast({
        title: 'Conversion Failed',
        description: error instanceof Error ? error.message : 'Failed to convert warranty to AMC',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold text-primary">
            Convert Warranty to AMC
          </DialogTitle>
          <DialogDescription>
            Convert warranty {warrantyDetails?.bslNo || warrantyId} for customer{' '}
            {warrantyDetails?.customerName} to an AMC contract.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="reason"
                render={({ field }) => (
                  <FormItem className="md:col-span-2">
                    <FormLabel>Conversion Reason *</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter reason for converting warranty to AMC..."
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="effectiveDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Effective Date *</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              'w-full pl-3 text-left font-normal',
                              !field.value && 'text-muted-foreground'
                            )}
                          >
                            {field.value ? (
                              format(field.value, 'PPP')
                            ) : (
                              <span>Pick a date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) =>
                            date < new Date() || date < new Date('1900-01-01')
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="amcData.natureOfService"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nature of Service *</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., Preventive Maintenance" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="border-t pt-4">
              <h3 className="text-lg font-medium mb-4">AMC Contract Details</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="amcData.startDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>AMC Start Date *</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant="outline"
                              className={cn(
                                'w-full pl-3 text-left font-normal',
                                !field.value && 'text-muted-foreground'
                              )}
                            >
                              {field.value ? (
                                format(field.value, 'PPP')
                              ) : (
                                <span>Pick a date</span>
                              )}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) => date < new Date('1900-01-01')}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="amcData.endDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>AMC End Date *</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant="outline"
                              className={cn(
                                'w-full pl-3 text-left font-normal',
                                !field.value && 'text-muted-foreground'
                              )}
                            >
                              {field.value ? (
                                format(field.value, 'PPP')
                              ) : (
                                <span>Pick a date</span>
                              )}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) => date < new Date('1900-01-01')}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="amcData.amount"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>AMC Amount *</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.01"
                          placeholder="0.00"
                          {...field}
                          onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="amcData.numberOfServices"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Number of Services</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="1"
                          {...field}
                          onChange={(e) => field.onChange(parseInt(e.target.value) || 4)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Additional Notes</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Any additional notes about this conversion..."
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Convert to AMC
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
