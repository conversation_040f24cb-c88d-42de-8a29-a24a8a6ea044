const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testWarrantyConversion() {
  try {
    console.log('🧪 Testing warranty to AMC conversion via API call...\n');

    const warrantyId = '9de7b408-38cf-43ea-ace7-7e46d787c48e';

    // Test data for conversion
    const conversionData = {
      sourceId: warrantyId,
      conversionType: 'WARRANTY_TO_AMC',
      reason: 'Warranty expired - converting to AMC for continued service coverage',
      effectiveDate: new Date().toISOString(),
      notes: 'Test conversion via API call',
      amcData: {
        startDate: new Date().toISOString(),
        endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(), // 1 year from now
        amount: 5000,
        numberOfServices: 4,
        natureOfService: 'Preventive Maintenance and Repair Services'
      }
    };

    console.log('📤 Making API call to conversion endpoint...');
    console.log('Data:', JSON.stringify(conversionData, null, 2));

    // Make API call to the conversion endpoint
    const response = await fetch('http://localhost:3001/api/conversions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // Note: In a real scenario, we'd need proper authentication headers
      },
      body: JSON.stringify(conversionData)
    });

    console.log(`\n📥 Response status: ${response.status}`);

    if (response.ok) {
      const result = await response.json();
      console.log('✅ Conversion successful!');
      console.log('Result:', JSON.stringify(result, null, 2));

      // Verify the conversion in the database
      console.log('\n🔍 Verifying conversion in database...');

      const updatedWarranty = await prisma.warranties.findUnique({
        where: { id: warrantyId },
        include: {
          customer: true
        }
      });

      console.log(`Warranty status: ${updatedWarranty.status}`);
      console.log(`AMC ID: ${updatedWarranty.amcId}`);

      if (updatedWarranty.amcId) {
        const amcContract = await prisma.amc_contracts.findUnique({
          where: { id: updatedWarranty.amcId },
          include: {
            customer: true,
            machines: true
          }
        });

        console.log('\n📋 Created AMC Contract:');
        console.log(`   ID: ${amcContract.id}`);
        console.log(`   Customer: ${amcContract.customer.name}`);
        console.log(`   Amount: ${amcContract.amount}`);
        console.log(`   Start Date: ${amcContract.startDate}`);
        console.log(`   End Date: ${amcContract.endDate}`);
        console.log(`   Machines: ${amcContract.machines.length}`);
      }

    } else {
      const errorData = await response.text();
      console.log('❌ Conversion failed!');
      console.log(`Error status: ${response.status}`);
      console.log('Error response:', errorData);

      // If it's an authentication error, let's try to understand the issue
      if (response.status === 401 || response.status === 403) {
        console.log('\n🔐 Authentication/Authorization issue detected');
        console.log('This is expected when calling the API without proper session cookies');
        console.log('The conversion functionality requires ADMIN or MANAGER role');

        // Let's still check if the warranty exists and is eligible
        console.log('\n🔍 Checking warranty eligibility directly in database...');
        const warranty = await prisma.warranties.findUnique({
          where: { id: warrantyId },
          include: {
            customer: true,
            machines: {
              include: {
                components: true
              }
            }
          }
        });

        if (warranty) {
          console.log('✅ Warranty found and appears eligible:');
          console.log(`   Customer: ${warranty.customer.name}`);
          console.log(`   Status: ${warranty.status}`);
          console.log(`   Machines: ${warranty.machines.length}`);
          console.log(`   Components: ${warranty.machines.reduce((total, machine) => total + machine.components.length, 0)}`);
          console.log(`   Executive ID: ${warranty.executiveId}`);

          if (warranty.status === 'CONVERTED') {
            console.log('⚠️  Warranty has already been converted');
          } else {
            console.log('✅ Warranty is eligible for conversion');
          }
        }
      }
    }

  } catch (error) {
    console.error('❌ Error testing conversion:', error);
    console.error('Error details:', error.message);
    console.error('Stack trace:', error.stack);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testWarrantyConversion();
