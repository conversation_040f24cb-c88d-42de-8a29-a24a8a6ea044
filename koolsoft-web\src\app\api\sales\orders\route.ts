import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { getSalesOrderRepository } from '@/lib/repositories';
import { createSalesOrderSchema, salesFilterSchema } from '@/lib/validations/sales.schema';
import { z } from 'zod';

/**
 * GET /api/sales/orders
 * Get sales orders with filtering, pagination, and sorting
 */
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  async (request: NextRequest) => {
    try {
      const { searchParams } = new URL(request.url);
      
      // Parse and validate query parameters
      const filters = {
        customerId: searchParams.get('customerId') || undefined,
        executiveId: searchParams.get('executiveId') || undefined,
        status: searchParams.get('status') || undefined,
        startDate: searchParams.get('startDate') || undefined,
        endDate: searchParams.get('endDate') || undefined,
        search: searchParams.get('search') || undefined,
        skip: searchParams.get('skip') || undefined,
        take: searchParams.get('take') || undefined,
        sortBy: searchParams.get('sortBy') || undefined,
        sortOrder: searchParams.get('sortOrder') || undefined,
      };

      const validatedFilters = salesFilterSchema.parse(filters);

      const salesOrderRepository = getSalesOrderRepository();
      const result = await salesOrderRepository.findWithFilters(validatedFilters);

      return NextResponse.json({
        success: true,
        data: result.data,
        pagination: result.pagination,
      });
    } catch (error) {
      console.error('Error fetching sales orders:', error);
      
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid request parameters',
            details: error.errors,
          },
          { status: 400 }
        );
      }
      
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to fetch sales orders',
        },
        { status: 500 }
      );
    }
  }
);

/**
 * POST /api/sales/orders
 * Create a new sales order
 */
export const POST = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  async (request: NextRequest) => {
    try {
      const body = await request.json();

      // Validate request body
      const validatedData = createSalesOrderSchema.parse(body);

      const salesOrderRepository = getSalesOrderRepository();

      // Create sales order
      const salesOrder = await salesOrderRepository.create({
        customerId: validatedData.customerId,
        executiveId: validatedData.executiveId,
        orderDate: validatedData.orderDate,
        contactPerson: validatedData.contactPerson,
        contactPhone: validatedData.contactPhone,
        status: validatedData.status,
        deliveryDate: validatedData.deliveryDate,
        actualDeliveryDate: validatedData.actualDeliveryDate,
        amount: validatedData.amount,
        remarks: validatedData.remarks,
        customer: {
          connect: { id: validatedData.customerId },
        },
        executive: {
          connect: { id: validatedData.executiveId },
        },
      });

      // Fetch the created order with relations
      const createdOrder = await salesOrderRepository.findById(salesOrder.id);

      return NextResponse.json({
        success: true,
        data: createdOrder,
        message: 'Sales order created successfully',
      }, { status: 201 });
    } catch (error) {
      console.error('Error creating sales order:', error);
      
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid request data',
            details: error.errors,
          },
          { status: 400 }
        );
      }
      
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to create sales order',
        },
        { status: 500 }
      );
    }
  }
);
