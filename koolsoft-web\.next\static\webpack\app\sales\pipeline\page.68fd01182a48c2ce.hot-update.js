"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/sales/pipeline/page",{

/***/ "(app-pages-browser)/./src/app/sales/pipeline/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/sales/pipeline/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SalesPipelinePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout */ \"(app-pages-browser)/./src/components/layout/index.ts\");\n/* harmony import */ var _components_sales_sales_pipeline_board__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/sales/sales-pipeline-board */ \"(app-pages-browser)/./src/components/sales/sales-pipeline-board.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Download_Filter_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Filter,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Filter_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Filter,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Filter_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Filter,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_6__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n/**\n * Sales Pipeline Page\n * \n * This page provides a kanban board interface for managing the sales pipeline.\n * It displays sales leads, opportunities, prospects, and orders in a visual\n * drag-and-drop interface organized by status.\n */ function SalesPipelinePage() {\n    const breadcrumbs = [\n        {\n            label: 'Sales',\n            href: '/sales'\n        },\n        {\n            label: 'Pipeline',\n            href: '/sales/pipeline'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout__WEBPACK_IMPORTED_MODULE_2__.DashboardLayout, {\n        title: \"Sales Pipeline\",\n        breadcrumbs: breadcrumbs,\n        requireAuth: true,\n        allowedRoles: [\n            'ADMIN',\n            'MANAGER',\n            'EXECUTIVE',\n            'USER'\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        className: \"bg-primary text-primary-foreground\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                        className: \"text-primary-foreground/80\",\n                                        children: \"Manage your sales pipeline with drag-and-drop kanban board\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\sales\\\\pipeline\\\\page.tsx\",\n                                        lineNumber: 33,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\sales\\\\pipeline\\\\page.tsx\",\n                                    lineNumber: 32,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            asChild: true,\n                                            variant: \"secondary\",\n                                            size: \"sm\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                                href: \"/leads/new\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Filter_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\sales\\\\pipeline\\\\page.tsx\",\n                                                        lineNumber: 40,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"New Lead\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\sales\\\\pipeline\\\\page.tsx\",\n                                                lineNumber: 39,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\sales\\\\pipeline\\\\page.tsx\",\n                                            lineNumber: 38,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            variant: \"secondary\",\n                                            size: \"sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Filter_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\sales\\\\pipeline\\\\page.tsx\",\n                                                    lineNumber: 45,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Filter\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\sales\\\\pipeline\\\\page.tsx\",\n                                            lineNumber: 44,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            variant: \"secondary\",\n                                            size: \"sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Filter_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\sales\\\\pipeline\\\\page.tsx\",\n                                                    lineNumber: 49,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Export\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\sales\\\\pipeline\\\\page.tsx\",\n                                            lineNumber: 48,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\sales\\\\pipeline\\\\page.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\sales\\\\pipeline\\\\page.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\sales\\\\pipeline\\\\page.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\sales\\\\pipeline\\\\page.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sales_sales_pipeline_board__WEBPACK_IMPORTED_MODULE_3__.SalesPipelineBoard, {}, void 0, false, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\sales\\\\pipeline\\\\page.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\sales\\\\pipeline\\\\page.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\sales\\\\pipeline\\\\page.tsx\",\n        lineNumber: 26,\n        columnNumber: 10\n    }, this);\n}\n_c1 = SalesPipelinePage;\n_c = SalesPipelinePage;\nvar _c;\n$RefreshReg$(_c, \"SalesPipelinePage\");\nvar _c1;\n$RefreshReg$(_c1, \"SalesPipelinePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/sales/pipeline/page.tsx\n"));

/***/ })

});