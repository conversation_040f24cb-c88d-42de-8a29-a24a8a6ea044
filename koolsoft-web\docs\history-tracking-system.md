# Comprehensive History Tracking System

## Overview

The KoolSoft application includes a comprehensive history tracking system that captures all customer interaction and service history across multiple domains. This system provides a centralized approach to tracking repairs, maintenance, warranty claims, AMC details, and audit activities.

## Architecture

### Core Components

1. **TypeScript Interfaces** (`src/types/history.types.ts`)
   - Strongly typed interfaces for all history record types
   - Comprehensive type definitions for customer interactions
   - Support for aggregated history overviews and activity summaries

2. **Validation Schemas** (`src/lib/validations/history.schema.ts`)
   - Zod-based validation for all history data types
   - Input validation for API endpoints
   - Type-safe form handling and data processing

3. **Repository Layer** (`src/lib/repositories/`)
   - Enhanced history card repository with comprehensive methods
   - Specialized history repair repository for repair tracking
   - Database abstraction with Prisma integration

4. **API Endpoints** (`src/app/api/history/`)
   - RESTful API for history data management
   - Role-based access control integration
   - Comprehensive filtering and pagination support

5. **UI Components** (`src/components/history/`)
   - React components for history display and management
   - Responsive design following established patterns
   - Real-time data updates and interactive features

## History Record Types

### 1. Repair History (`HistoryRepair`)
Tracks service calls, technician visits, and parts replacement:

```typescript
interface HistoryRepair {
  id: string;
  historyCardId: string;
  repairDate: Date | string;
  technicianId?: string;
  description: string;
  partsReplaced?: string[];
  cost?: number;
  status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  notes?: string;
  followUpRequired?: boolean;
  followUpDate?: Date | string;
}
```

### 2. Maintenance History (`HistoryMaintenance`)
Tracks scheduled maintenance and preventive care:

```typescript
interface HistoryMaintenance {
  id: string;
  historyCardId: string;
  maintenanceType: 'PREVENTIVE' | 'CORRECTIVE' | 'SCHEDULED' | 'EMERGENCY';
  scheduledDate: Date | string;
  completedDate?: Date | string;
  technicianId?: string;
  description: string;
  checklistItems?: MaintenanceChecklistItem[];
  status: 'SCHEDULED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED' | 'OVERDUE';
}
```

### 3. Warranty History (`HistoryWarranty`)
Tracks warranty claims and coverage periods:

```typescript
interface HistoryWarranty {
  id: string;
  historyCardId: string;
  warrantyType: 'IN_WARRANTY' | 'OUT_WARRANTY' | 'EXTENDED';
  claimNumber?: string;
  claimDate: Date | string;
  issueDescription: string;
  resolution?: string;
  status: 'SUBMITTED' | 'UNDER_REVIEW' | 'APPROVED' | 'REJECTED' | 'COMPLETED';
  coverageStartDate: Date | string;
  coverageEndDate: Date | string;
}
```

### 4. AMC History (`HistoryAmcDetail`)
Tracks Annual Maintenance Contract details:

```typescript
interface HistoryAmcDetail {
  id: string;
  historyCardId: string;
  amcId: string;
  contractNumber: string;
  startDate: Date | string;
  endDate: Date | string;
  serviceVisits: number;
  completedVisits: number;
  remainingVisits: number;
  contractValue: number;
  status: 'ACTIVE' | 'EXPIRED' | 'CANCELLED' | 'RENEWED';
}
```

### 5. Audit History (`HistoryAudit`)
Tracks system changes and user actions:

```typescript
interface HistoryAudit {
  id: string;
  historyCardId: string;
  auditType: 'SYSTEM' | 'USER_ACTION' | 'DATA_CHANGE' | 'ACCESS' | 'SECURITY';
  action: string;
  entityType: string;
  entityId: string;
  oldValues?: Record<string, any>;
  newValues?: Record<string, any>;
  userId: string;
  timestamp: Date | string;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
}
```

## API Endpoints

### History Repairs
- `GET /api/history/repairs` - List repair history with filtering
- `POST /api/history/repairs` - Create new repair record
- `GET /api/history/repairs/[id]` - Get specific repair record
- `PUT /api/history/repairs/[id]` - Update repair record
- `DELETE /api/history/repairs/[id]` - Delete repair record

### History Overview
- `GET /api/history/overview?customerId={id}` - Get customer history overview
- `GET /api/history/overview?dashboard=true` - Get dashboard statistics

### Query Parameters
All list endpoints support the following query parameters:
- `customerId` - Filter by customer ID
- `dateFrom` - Filter records from date
- `dateTo` - Filter records to date
- `status` - Filter by status (multiple values supported)
- `priority` - Filter by priority (multiple values supported)
- `technicianId` - Filter by technician ID
- `page` - Page number for pagination (default: 1)
- `limit` - Records per page (default: 10, max: 100)
- `sortBy` - Sort field (default: 'createdAt')
- `sortOrder` - Sort order: 'asc' or 'desc' (default: 'desc')

## Usage Examples

### 1. Creating a Repair Record

```typescript
import { createHistoryRepairSchema } from '@/lib/validations/history.schema';

const repairData = {
  historyCardId: 'card-uuid',
  repairDate: new Date(),
  description: 'Replaced faulty compressor',
  technicianId: 'tech-uuid',
  partsReplaced: ['Compressor Unit', 'Gasket Set'],
  cost: 2500.00,
  status: 'COMPLETED',
  priority: 'HIGH'
};

// Validate data
const validatedData = createHistoryRepairSchema.parse(repairData);

// Create via API
const response = await fetch('/api/history/repairs', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  credentials: 'include',
  body: JSON.stringify(validatedData)
});
```

### 2. Fetching Customer History Overview

```typescript
const customerId = 'customer-uuid';
const response = await fetch(
  `/api/history/overview?customerId=${customerId}&includeStatistics=true`,
  { credentials: 'include' }
);

const { data } = await response.json();
const { overview, statistics } = data;

console.log(`Total repairs: ${overview.totalRepairs}`);
console.log(`Active AMCs: ${overview.activeAMCs}`);
console.log(`Average repair cost: ${statistics.totalCost / statistics.totalRepairs}`);
```

### 3. Using the History Overview Component

```tsx
import { HistoryOverview } from '@/components/history/history-overview';

function CustomerDetailPage({ customerId }: { customerId: string }) {
  return (
    <div className="space-y-6">
      <h1>Customer History</h1>
      <HistoryOverview 
        customerId={customerId}
        className="w-full"
      />
    </div>
  );
}
```

## Integration Guidelines

### 1. Repository Usage

```typescript
import { HistoryCardRepository, HistoryRepairRepository } from '@/lib/repositories';

// Initialize repositories
const historyCardRepo = new HistoryCardRepository();
const historyRepairRepo = new HistoryRepairRepository();

// Get customer overview
const overview = await historyCardRepo.getCustomerHistoryOverview(customerId);

// Get repair statistics
const stats = await historyRepairRepo.getRepairStatistics(customerId);

// Find repairs by technician
const repairs = await historyRepairRepo.findByTechnicianId(technicianId, {
  page: 1,
  limit: 20,
  dateFrom: new Date('2024-01-01'),
  dateTo: new Date('2024-12-31')
});
```

### 2. Validation Schema Usage

```typescript
import { 
  createHistoryRepairSchema,
  updateHistoryRepairSchema,
  historyFilterSchema 
} from '@/lib/validations/history.schema';

// Validate create data
try {
  const validData = createHistoryRepairSchema.parse(inputData);
  // Proceed with creation
} catch (error) {
  if (error instanceof z.ZodError) {
    console.error('Validation errors:', error.errors);
  }
}

// Validate filter parameters
const filters = historyFilterSchema.parse(queryParams);
```

### 3. Role-Based Access Control

All history API endpoints implement role-based access control:

- **ADMIN**: Full access to all operations
- **MANAGER**: Full access to all operations
- **EXECUTIVE**: Read and create access, limited update/delete
- **USER**: Read-only access

```typescript
// API routes automatically enforce permissions
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'], 
  getRepairHistory
);

export const POST = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'], 
  createRepairHistory
);
```

## Database Schema Integration

The history tracking system integrates with existing Prisma models:

- `history_cards` - Main history card records
- `history_repairs` - Repair history records
- `history_maintenance` - Maintenance history records
- `history_complaints` - Complaint history records
- `history_amc_details` - AMC history records
- `history_audits` - Audit history records

All models include:
- UUID primary keys
- Foreign key relationships to history cards
- Timestamps for creation and updates
- Indexes for optimal query performance

## Performance Considerations

1. **Pagination**: All list endpoints support pagination to handle large datasets
2. **Indexing**: Database indexes on frequently queried fields (dates, IDs, status)
3. **Filtering**: Server-side filtering reduces data transfer
4. **Caching**: Consider implementing Redis caching for frequently accessed data
5. **Lazy Loading**: UI components load data on demand

## Security Features

1. **Authentication**: All endpoints require valid session authentication
2. **Authorization**: Role-based access control for different user types
3. **Input Validation**: Comprehensive Zod schema validation
4. **SQL Injection Protection**: Prisma ORM provides built-in protection
5. **Activity Logging**: All operations are logged for audit purposes

## Error Handling

The system implements comprehensive error handling:

```typescript
// API responses follow consistent format
{
  success: boolean;
  data?: any;
  error?: string;
  message?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Validation errors include detailed information
{
  success: false;
  error: 'Invalid request data';
  details: [
    {
      path: ['repairDate'],
      message: 'Valid repair date is required'
    }
  ];
}
```

## Future Enhancements

1. **Real-time Updates**: WebSocket integration for live updates
2. **Advanced Analytics**: Machine learning for predictive maintenance
3. **Mobile App**: Native mobile application for field technicians
4. **Document Management**: File attachment and document storage
5. **Workflow Automation**: Automated task creation and notifications
6. **Integration APIs**: Third-party system integration capabilities
7. **Reporting Engine**: Advanced reporting and dashboard features
8. **Audit Trail**: Enhanced audit logging with detailed change tracking
