/**
 * Comprehensive History Tracking Types
 * 
 * This file contains all TypeScript interfaces for the KoolSoft history tracking system.
 * It provides strongly typed interfaces for different types of history records including
 * repairs, maintenance, warranty, AMC, and audit history.
 */

// Base interfaces for common fields
export interface BaseHistoryRecord {
  id: string;
  createdAt: Date | string;
  updatedAt: Date | string;
  originalId?: number;
}

export interface CustomerReference {
  id: string;
  name: string;
  address?: string;
  city?: string;
  state?: string;
  phone?: string;
  email?: string;
}

export interface UserReference {
  id: string;
  name: string;
  email: string;
  role: string;
}

// History Card Types
export interface HistoryCard extends BaseHistoryRecord {
  cardNo?: number;
  source?: 'AMC' | 'INW' | 'OTW' | 'SERVICE';
  customerId: string;
  amcId?: string;
  inWarrantyId?: string;
  outWarrantyId?: string;
  toCardNo?: number;
  customer?: CustomerReference;
  sections?: HistorySection[];
  repairs?: HistoryRepair[];
  maintenance?: HistoryMaintenance[];
  complaints?: HistoryComplaint[];
  amcDetails?: HistoryAmcDetail[];
  addonDetails?: HistoryAddonDetail[];
  waterWashes?: HistoryWaterWash[];
  audits?: HistoryAudit[];
}

export interface HistorySection extends BaseHistoryRecord {
  historyCardId: string;
  sectionCode: string;
  content: string;
}

// Repair History Types
export interface HistoryRepair extends BaseHistoryRecord {
  historyCardId: string;
  assetNo?: number;
  repairDate: Date | string;
  technicianId?: string;
  description: string;
  partsReplaced?: string[];
  laborHours?: number;
  cost?: number;
  status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  notes?: string;
  followUpRequired?: boolean;
  followUpDate?: Date | string;
  technician?: UserReference;
  historyCard?: HistoryCard;
}

// Maintenance History Types
export interface HistoryMaintenance extends BaseHistoryRecord {
  historyCardId: string;
  maintenanceType: 'PREVENTIVE' | 'CORRECTIVE' | 'SCHEDULED' | 'EMERGENCY';
  scheduledDate: Date | string;
  completedDate?: Date | string;
  technicianId?: string;
  description: string;
  checklistItems?: MaintenanceChecklistItem[];
  status: 'SCHEDULED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED' | 'OVERDUE';
  cost?: number;
  notes?: string;
  nextMaintenanceDate?: Date | string;
  technician?: UserReference;
  historyCard?: HistoryCard;
}

export interface MaintenanceChecklistItem {
  id: string;
  description: string;
  completed: boolean;
  notes?: string;
}

// Warranty History Types
export interface HistoryWarranty extends BaseHistoryRecord {
  historyCardId: string;
  warrantyType: 'IN_WARRANTY' | 'OUT_WARRANTY' | 'EXTENDED';
  claimNumber?: string;
  claimDate: Date | string;
  issueDescription: string;
  resolution?: string;
  status: 'SUBMITTED' | 'UNDER_REVIEW' | 'APPROVED' | 'REJECTED' | 'COMPLETED';
  coverageStartDate: Date | string;
  coverageEndDate: Date | string;
  claimAmount?: number;
  approvedAmount?: number;
  rejectionReason?: string;
  documents?: WarrantyDocument[];
  historyCard?: HistoryCard;
}

export interface WarrantyDocument {
  id: string;
  fileName: string;
  fileType: string;
  fileSize: number;
  uploadedAt: Date | string;
  uploadedBy: string;
}

// AMC History Types
export interface HistoryAmcDetail extends BaseHistoryRecord {
  historyCardId: string;
  amcId: string;
  contractNumber: string;
  startDate: Date | string;
  endDate: Date | string;
  renewalDate?: Date | string;
  serviceVisits: number;
  completedVisits: number;
  remainingVisits: number;
  contractValue: number;
  status: 'ACTIVE' | 'EXPIRED' | 'CANCELLED' | 'RENEWED';
  serviceType: string;
  coverageDetails?: string;
  notes?: string;
  historyCard?: HistoryCard;
}

// Addon History Types
export interface HistoryAddonDetail extends BaseHistoryRecord {
  historyCardId: string;
  addonType: string;
  addonDate: Date | string;
  description: string;
  cost?: number;
  validFrom: Date | string;
  validTo?: Date | string;
  status: 'ACTIVE' | 'EXPIRED' | 'CANCELLED';
  notes?: string;
  historyCard?: HistoryCard;
}

// Water Wash History Types
export interface HistoryWaterWash extends BaseHistoryRecord {
  historyCardId: string;
  washDate: Date | string;
  technicianId?: string;
  washType: 'REGULAR' | 'DEEP_CLEAN' | 'CHEMICAL' | 'MAINTENANCE';
  duration?: number; // in minutes
  chemicalsUsed?: string[];
  cost?: number;
  notes?: string;
  beforePhotos?: string[];
  afterPhotos?: string[];
  technician?: UserReference;
  historyCard?: HistoryCard;
}

// Complaint History Types
export interface HistoryComplaint extends BaseHistoryRecord {
  historyCardId: string;
  complaintNumber: string;
  complaintDate: Date | string;
  visitDate?: Date | string;
  assetNo?: number;
  natureOfComplaint: string;
  complaintCategory: 'TECHNICAL' | 'SERVICE' | 'BILLING' | 'PRODUCT' | 'OTHER';
  description: string;
  action?: string;
  resolution?: string;
  status: 'OPEN' | 'IN_PROGRESS' | 'RESOLVED' | 'CLOSED' | 'ESCALATED';
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  assignedTo?: string;
  responseTime?: number; // in hours
  resolutionTime?: number; // in hours
  customerSatisfaction?: number; // 1-5 rating
  followUpRequired?: boolean;
  followUpDate?: Date | string;
  notes?: string;
  assignedUser?: UserReference;
  historyCard?: HistoryCard;
}

// Audit History Types
export interface HistoryAudit extends BaseHistoryRecord {
  historyCardId: string;
  auditType: 'SYSTEM' | 'USER_ACTION' | 'DATA_CHANGE' | 'ACCESS' | 'SECURITY';
  action: string;
  entityType: string;
  entityId: string;
  oldValues?: Record<string, any>;
  newValues?: Record<string, any>;
  userId: string;
  ipAddress?: string;
  userAgent?: string;
  timestamp: Date | string;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  description?: string;
  user?: UserReference;
  historyCard?: HistoryCard;
}

// Component Replacement History Types
export interface HistoryComponentReplacement extends BaseHistoryRecord {
  historyCardId: string;
  componentId: string;
  componentName: string;
  replacementDate: Date | string;
  oldComponentSerialNumber?: string;
  newComponentSerialNumber?: string;
  reason: 'MAINTENANCE' | 'FAILURE' | 'UPGRADE' | 'WARRANTY';
  technicianId?: string;
  cost?: number;
  warrantyPeriod?: number; // in months
  notes?: string;
  technician?: UserReference;
  historyCard?: HistoryCard;
}

// Aggregated History Types for UI
export interface CustomerHistoryOverview {
  customerId: string;
  customer: CustomerReference;
  totalHistoryCards: number;
  totalRepairs: number;
  totalMaintenance: number;
  totalComplaints: number;
  totalWarrantyClaims: number;
  activeAMCs: number;
  lastServiceDate?: Date | string;
  nextMaintenanceDate?: Date | string;
  recentActivity: HistoryActivitySummary[];
}

export interface HistoryActivitySummary {
  id: string;
  type: 'REPAIR' | 'MAINTENANCE' | 'COMPLAINT' | 'WARRANTY' | 'AMC' | 'AUDIT';
  date: Date | string;
  description: string;
  status?: string;
  priority?: string;
}

// Filter and Search Types
export interface HistoryFilterOptions {
  customerId?: string;
  dateFrom?: Date | string;
  dateTo?: Date | string;
  historyType?: string[];
  status?: string[];
  priority?: string[];
  technicianId?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// Export Types for API Responses
export interface HistoryApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface HistoryListResponse<T> extends HistoryApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}
