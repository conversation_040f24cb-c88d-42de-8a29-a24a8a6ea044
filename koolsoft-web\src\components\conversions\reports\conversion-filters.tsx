'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarIcon, Filter, X } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { ConversionReportFilter } from '@/lib/validations/conversion.schema';
import { SearchableCustomerSelect } from '@/components/ui/searchable-customer-select';

interface ConversionFiltersProps {
  filters: ConversionReportFilter;
  onFiltersChange: (filters: Partial<ConversionReportFilter>) => void;
  className?: string;
}

/**
 * Conversion Filters Component
 * 
 * Advanced filtering component for conversion reports
 */
export function ConversionFilters({ 
  filters, 
  onFiltersChange, 
  className = '' 
}: ConversionFiltersProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const handleDateFromChange = (date: Date | undefined) => {
    onFiltersChange({ dateFrom: date });
  };

  const handleDateToChange = (date: Date | undefined) => {
    onFiltersChange({ dateTo: date });
  };

  const handleCustomerChange = (customerId: string) => {
    onFiltersChange({ customerId: customerId === 'all' ? undefined : customerId });
  };

  const handleConversionTypeChange = (type: string) => {
    onFiltersChange({ 
      conversionType: type === 'all' ? undefined : type as 'WARRANTY_TO_AMC' | 'AMC_TO_OUT_WARRANTY' | 'WARRANTY_TO_OUT_WARRANTY'
    });
  };

  const handleAmountMinChange = (value: string) => {
    const amount = value ? parseFloat(value) : undefined;
    onFiltersChange({ amountMin: amount });
  };

  const handleAmountMaxChange = (value: string) => {
    const amount = value ? parseFloat(value) : undefined;
    onFiltersChange({ amountMax: amount });
  };

  const clearFilters = () => {
    onFiltersChange({
      dateFrom: undefined,
      dateTo: undefined,
      conversionType: undefined,
      customerId: undefined,
      status: undefined,
      userId: undefined,
      amountMin: undefined,
      amountMax: undefined,
    });
  };

  const hasActiveFilters = !!(
    filters.dateFrom ||
    filters.dateTo ||
    filters.conversionType ||
    filters.customerId ||
    filters.status ||
    filters.userId ||
    filters.amountMin ||
    filters.amountMax
  );

  return (
    <Card className={className}>
      <CardHeader className="bg-primary text-white">
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filters
          </div>
          <div className="flex items-center gap-2">
            {hasActiveFilters && (
              <Button
                variant="outline"
                size="sm"
                onClick={clearFilters}
                className="bg-white text-primary hover:bg-gray-100"
              >
                <X className="h-4 w-4 mr-1" />
                Clear
              </Button>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              className="bg-white text-primary hover:bg-gray-100"
            >
              {isExpanded ? 'Collapse' : 'Expand'}
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="p-6">
        {/* Basic Filters - Always Visible */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Date From */}
          <div className="space-y-2">
            <Label htmlFor="dateFrom">From Date</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !filters.dateFrom && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {filters.dateFrom ? format(filters.dateFrom, "PPP") : "Pick a date"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={filters.dateFrom}
                  onSelect={handleDateFromChange}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>

          {/* Date To */}
          <div className="space-y-2">
            <Label htmlFor="dateTo">To Date</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !filters.dateTo && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {filters.dateTo ? format(filters.dateTo, "PPP") : "Pick a date"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={filters.dateTo}
                  onSelect={handleDateToChange}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>

          {/* Conversion Type */}
          <div className="space-y-2">
            <Label htmlFor="conversionType">Conversion Type</Label>
            <Select
              value={filters.conversionType || 'all'}
              onValueChange={handleConversionTypeChange}
            >
              <SelectTrigger>
                <SelectValue placeholder="All types" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="WARRANTY_TO_AMC">Warranty → AMC</SelectItem>
                <SelectItem value="AMC_TO_OUT_WARRANTY">AMC → Out-of-Warranty</SelectItem>
                <SelectItem value="WARRANTY_TO_OUT_WARRANTY">Warranty → Out-of-Warranty</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Customer */}
          <div className="space-y-2">
            <Label htmlFor="customer">Customer</Label>
            <div className="flex gap-2">
              <Button
                variant={!filters.customerId ? 'default' : 'outline'}
                size="sm"
                onClick={() => handleCustomerChange('all')}
                className="flex-shrink-0"
              >
                All
              </Button>
              <SearchableCustomerSelect
                value={filters.customerId || ''}
                onValueChange={handleCustomerChange}
                placeholder="Select customer..."
                className="flex-1"
              />
            </div>
          </div>
        </div>

        {/* Advanced Filters - Collapsible */}
        {isExpanded && (
          <div className="mt-6 pt-6 border-t">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Amount Range */}
              <div className="space-y-2">
                <Label htmlFor="amountMin">Min Amount</Label>
                <Input
                  id="amountMin"
                  type="number"
                  placeholder="0"
                  value={filters.amountMin || ''}
                  onChange={(e) => handleAmountMinChange(e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="amountMax">Max Amount</Label>
                <Input
                  id="amountMax"
                  type="number"
                  placeholder="No limit"
                  value={filters.amountMax || ''}
                  onChange={(e) => handleAmountMaxChange(e.target.value)}
                />
              </div>

              {/* Status */}
              <div className="space-y-2">
                <Label htmlFor="status">Status</Label>
                <Select
                  value={filters.status || 'all'}
                  onValueChange={(value) => onFiltersChange({ 
                    status: value === 'all' ? undefined : value as 'SUCCESS' | 'FAILED' | 'PENDING'
                  })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All statuses" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="SUCCESS">Success</SelectItem>
                    <SelectItem value="FAILED">Failed</SelectItem>
                    <SelectItem value="PENDING">Pending</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Order By */}
              <div className="space-y-2">
                <Label htmlFor="orderBy">Sort By</Label>
                <Select
                  value={filters.orderBy}
                  onValueChange={(value) => onFiltersChange({ 
                    orderBy: value as 'createdAt' | 'effectiveDate' | 'amount'
                  })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="createdAt">Date Created</SelectItem>
                    <SelectItem value="effectiveDate">Effective Date</SelectItem>
                    <SelectItem value="amount">Amount</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Sort Direction */}
            <div className="mt-4">
              <div className="flex items-center gap-4">
                <Label>Sort Direction:</Label>
                <div className="flex items-center gap-2">
                  <Button
                    variant={filters.orderDirection === 'desc' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => onFiltersChange({ orderDirection: 'desc' })}
                  >
                    Newest First
                  </Button>
                  <Button
                    variant={filters.orderDirection === 'asc' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => onFiltersChange({ orderDirection: 'asc' })}
                  >
                    Oldest First
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Active Filters Summary */}
        {hasActiveFilters && (
          <div className="mt-4 pt-4 border-t">
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <span>Active filters:</span>
              {filters.dateFrom && (
                <span className="bg-primary/10 text-primary px-2 py-1 rounded">
                  From: {format(filters.dateFrom, 'dd/MM/yyyy')}
                </span>
              )}
              {filters.dateTo && (
                <span className="bg-primary/10 text-primary px-2 py-1 rounded">
                  To: {format(filters.dateTo, 'dd/MM/yyyy')}
                </span>
              )}
              {filters.conversionType && (
                <span className="bg-primary/10 text-primary px-2 py-1 rounded">
                  Type: {filters.conversionType.replace(/_/g, ' → ')}
                </span>
              )}
              {filters.amountMin && (
                <span className="bg-primary/10 text-primary px-2 py-1 rounded">
                  Min: ₹{filters.amountMin}
                </span>
              )}
              {filters.amountMax && (
                <span className="bg-primary/10 text-primary px-2 py-1 rounded">
                  Max: ₹{filters.amountMax}
                </span>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
