'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  ChevronLeft, 
  ChevronRight, 
  FileText, 
  AlertCircle,
  ArrowRightLeft,
  ExternalLink
} from 'lucide-react';
import { ConversionReportFilter } from '@/lib/validations/conversion.schema';
import { format } from 'date-fns';
import { toast } from 'sonner';

interface ConversionDetailsTableProps {
  filters: ConversionReportFilter;
  onFiltersChange: (filters: Partial<ConversionReportFilter>) => void;
  className?: string;
  showTitle?: boolean;
}

interface ConversionDetail {
  id: string;
  cardNo: number | null;
  source: string;
  customerId: string;
  amcId: string | null;
  inWarrantyId: string | null;
  outWarrantyId: string | null;
  createdAt: string;
  customer: {
    id: string;
    name: string;
    city: string;
  };
  amcContract?: {
    id: string;
    amount: number;
    startDate: string;
    endDate: string;
    status: string;
  };
  inWarranty?: {
    id: string;
    bslNo: string;
    installDate: string;
    warrantyDate: string;
  };
  outWarranty?: {
    id: string;
    startDate: string;
    endDate: string;
    amount: number;
  };
}

interface ConversionDetailsResponse {
  conversions: ConversionDetail[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

/**
 * Conversion Details Table Component
 * 
 * Displays detailed conversion records with filtering and pagination
 */
export function ConversionDetailsTable({ 
  filters, 
  onFiltersChange, 
  className = '',
  showTitle = true 
}: ConversionDetailsTableProps) {
  const [data, setData] = useState<ConversionDetailsResponse | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchConversions();
  }, [filters]);

  const fetchConversions = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const searchParams = new URLSearchParams();
      
      // Add filters to search params
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          if (value instanceof Date) {
            searchParams.append(key, value.toISOString());
          } else {
            searchParams.append(key, value.toString());
          }
        }
      });

      const response = await fetch(`/api/conversions/reports/details?${searchParams}`, {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to fetch conversion details');
      }

      const result = await response.json();
      setData(result);
    } catch (error) {
      console.error('Error fetching conversion details:', error);
      setError('Failed to load conversion details');
      toast.error('Failed to load conversion details');
    } finally {
      setIsLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatConversionType = (type: string) => {
    switch (type) {
      case 'WARRANTY_TO_AMC':
        return 'Warranty → AMC';
      case 'AMC_TO_OUT_WARRANTY':
        return 'AMC → Out-of-Warranty';
      case 'WARRANTY_TO_OUT_WARRANTY':
        return 'Warranty → Out-of-Warranty';
      default:
        return type;
    }
  };

  const getConversionAmount = (conversion: ConversionDetail) => {
    if (conversion.amcContract?.amount) {
      return formatCurrency(conversion.amcContract.amount);
    }
    if (conversion.outWarranty?.amount) {
      return formatCurrency(Number(conversion.outWarranty.amount));
    }
    return '-';
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'WARRANTY_TO_AMC':
        return 'bg-green-100 text-green-800';
      case 'AMC_TO_OUT_WARRANTY':
        return 'bg-yellow-100 text-yellow-800';
      case 'WARRANTY_TO_OUT_WARRANTY':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handlePageChange = (newPage: number) => {
    onFiltersChange({ page: newPage });
  };

  if (isLoading) {
    return (
      <Card className={className}>
        {showTitle && (
          <CardHeader className="bg-primary text-white">
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Conversion Details
            </CardTitle>
          </CardHeader>
        )}
        <CardContent className="p-6">
          <div className="space-y-4">
            <Skeleton className="h-8 w-full" />
            <Skeleton className="h-8 w-full" />
            <Skeleton className="h-8 w-full" />
            <Skeleton className="h-8 w-full" />
            <Skeleton className="h-8 w-full" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error || !data) {
    return (
      <Card className={className}>
        {showTitle && (
          <CardHeader className="bg-primary text-white">
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Conversion Details
            </CardTitle>
          </CardHeader>
        )}
        <CardContent className="p-6">
          <div className="flex items-center gap-2 text-destructive">
            <AlertCircle className="h-5 w-5" />
            <span>{error || 'Failed to load conversion details'}</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      {showTitle && (
        <CardHeader className="bg-primary text-white">
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Conversion Details
            </div>
            <Badge variant="secondary" className="bg-white text-primary">
              {data.total} records
            </Badge>
          </CardTitle>
        </CardHeader>
      )}
      <CardContent className="p-0">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Card No.</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Customer</TableHead>
                <TableHead>City</TableHead>
                <TableHead>Amount</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {data.conversions.map((conversion) => (
                <TableRow key={conversion.id}>
                  <TableCell className="font-medium">
                    {conversion.cardNo || '-'}
                  </TableCell>
                  <TableCell>
                    <Badge className={getTypeColor(conversion.source)}>
                      {formatConversionType(conversion.source)}
                    </Badge>
                  </TableCell>
                  <TableCell className="font-medium">
                    {conversion.customer.name}
                  </TableCell>
                  <TableCell>{conversion.customer.city}</TableCell>
                  <TableCell className="font-medium">
                    {getConversionAmount(conversion)}
                  </TableCell>
                  <TableCell>
                    {format(new Date(conversion.createdAt), 'dd/MM/yyyy')}
                  </TableCell>
                  <TableCell>
                    <Badge className="bg-green-100 text-green-800">
                      Success
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => window.open(`/history-cards/${conversion.id}`, '_blank')}
                      >
                        <ExternalLink className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        {/* Pagination */}
        {data.totalPages > 1 && (
          <div className="flex items-center justify-between p-4 border-t">
            <div className="text-sm text-muted-foreground">
              Showing {((data.page - 1) * data.limit) + 1} to {Math.min(data.page * data.limit, data.total)} of {data.total} results
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(data.page - 1)}
                disabled={data.page <= 1}
              >
                <ChevronLeft className="h-4 w-4" />
                Previous
              </Button>
              <span className="text-sm font-medium">
                Page {data.page} of {data.totalPages}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(data.page + 1)}
                disabled={data.page >= data.totalPages}
              >
                Next
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
