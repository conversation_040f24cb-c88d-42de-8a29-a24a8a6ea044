import { NextRequest, NextResponse } from 'next/server';
import { getOutWarrantyRepository } from '@/lib/repositories';
import { updateOutWarrantySchema } from '@/lib/validations/warranty.schema';
import { withRoleProtection } from '@/lib/auth/middleware';
import { z } from 'zod';

/**
 * GET /api/out-warranties/[id]
 * Get a specific out-warranty by ID
 */
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  async (
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      const { id } = await params;

      const outWarrantyRepository = getOutWarrantyRepository();

      // Get out-warranty with all related data
      const outWarranty = await outWarrantyRepository.findWithAllRelations(id);

      if (!outWarranty) {
        return NextResponse.json(
          { error: 'Out-warranty not found' },
          { status: 404 }
        );
      }

      return NextResponse.json(outWarranty);
    } catch (error) {
      console.error('Error fetching out-warranty:', error);
      return NextResponse.json(
        { error: 'Failed to fetch out-warranty' },
        { status: 500 }
      );
    }
  }
);

/**
 * PUT /api/out-warranties/[id]
 * Update a specific out-warranty
 */
export const PUT = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  async (
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      const { id } = await params;
      const body = await request.json();

      // Validate request body
      try {
        const validatedData = updateOutWarrantySchema.parse({ ...body, id });

        const outWarrantyRepository = getOutWarrantyRepository();

        // Check if out-warranty exists
        const existingOutWarranty = await outWarrantyRepository.findById(id);
        if (!existingOutWarranty) {
          return NextResponse.json(
            { error: 'Out-warranty not found' },
            { status: 404 }
          );
        }

        // Remove id from update data
        const { id: _, ...updateData } = validatedData;

        // Update out-warranty (dates are already converted by the schema)
        const updatedOutWarranty = await outWarrantyRepository.update(id, updateData);

        return NextResponse.json(updatedOutWarranty);
      } catch (error) {
        if (error instanceof z.ZodError) {
          return NextResponse.json(
            { error: 'Validation error', details: error.errors },
            { status: 400 }
          );
        }
        throw error;
      }
    } catch (error) {
      console.error('Error updating out-warranty:', error);
      return NextResponse.json(
        { error: 'Failed to update out-warranty' },
        { status: 500 }
      );
    }
  }
);

/**
 * DELETE /api/out-warranties/[id]
 * Delete a specific out-warranty
 */
export const DELETE = withRoleProtection(
  ['ADMIN', 'MANAGER'],
  async (
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      const { id } = await params;

      const outWarrantyRepository = getOutWarrantyRepository();

      // Check if out-warranty exists
      const existingOutWarranty = await outWarrantyRepository.findById(id);
      if (!existingOutWarranty) {
        return NextResponse.json(
          { error: 'Out-warranty not found' },
          { status: 404 }
        );
      }

      // Check if there are related payments that would prevent deletion
      const outWarrantyWithRelations = await outWarrantyRepository.findWithAllRelations(id);
      if (outWarrantyWithRelations?.payments && outWarrantyWithRelations.payments.length > 0) {
        return NextResponse.json(
          { error: 'Cannot delete out-warranty with existing payments. Please remove payments first.' },
          { status: 400 }
        );
      }

      // Delete the out-warranty
      await outWarrantyRepository.delete(id);

      return NextResponse.json({ message: 'Out-warranty deleted successfully' });
    } catch (error) {
      console.error('Error deleting out-warranty:', error);
      return NextResponse.json(
        { error: 'Failed to delete out-warranty' },
        { status: 500 }
      );
    }
  }
);
