'use client';

import React, { useState, useEffect } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  CalendarIcon, 
  Save, 
  X, 
  AlertCircle,
  Plus,
  Trash2,
  User,
  FileText
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import { createServiceReportSchema } from '@/lib/validations/service.schema';

type ServiceReportFormData = z.infer<typeof createServiceReportSchema>;

interface ServiceReportFormProps {
  onSubmit: (data: ServiceReportFormData) => Promise<void>;
  onCancel: () => void;
  initialData?: Partial<ServiceReportFormData>;
  isLoading?: boolean;
}

export function ServiceReportForm({
  onSubmit,
  onCancel,
  initialData,
  isLoading = false,
}: ServiceReportFormProps) {
  const [customers, setCustomers] = useState<any[]>([]);
  const [executives, setExecutives] = useState<any[]>([]);
  const [loadingData, setLoadingData] = useState(true);

  const form = useForm<ServiceReportFormData>({
    resolver: zodResolver(createServiceReportSchema),
    defaultValues: {
      customerId: initialData?.customerId || '',
      executiveId: initialData?.executiveId || '',
      reportDate: initialData?.reportDate || new Date(),
      visitDate: initialData?.visitDate,
      completionDate: initialData?.completionDate,
      natureOfService: initialData?.natureOfService || '',
      complaintType: initialData?.complaintType || 'REPAIR',
      actionTaken: initialData?.actionTaken || '',
      remarks: initialData?.remarks || '',
      status: initialData?.status || 'OPEN',
      details: initialData?.details || [
        {
          machineType: '',
          serialNumber: '',
          problem: '',
          solution: '',
          partReplaced: '',
        },
      ],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'details',
  });

  // Load customers and executives
  useEffect(() => {
    const loadData = async () => {
      try {
        const [customersRes, executivesRes] = await Promise.all([
          fetch('/api/customers?limit=1000', { credentials: 'include' }),
          fetch('/api/users?role=EXECUTIVE&limit=1000', { credentials: 'include' }),
        ]);

        if (customersRes.ok) {
          const customersData = await customersRes.json();
          setCustomers(customersData.success ? customersData.data : customersData.customers || []);
        }

        if (executivesRes.ok) {
          const executivesData = await executivesRes.json();
          setExecutives(executivesData.users || []);
        }
      } catch (error) {
        console.error('Error loading form data:', error);
      } finally {
        setLoadingData(false);
      }
    };

    loadData();
  }, []);

  const handleSubmit = async (data: ServiceReportFormData) => {
    try {
      await onSubmit(data);
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  const addServiceDetail = () => {
    append({
      machineType: '',
      serialNumber: '',
      problem: '',
      solution: '',
      partReplaced: '',
    });
  };

  const removeServiceDetail = (index: number) => {
    if (fields.length > 1) {
      remove(index);
    }
  };

  if (loadingData) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="text-sm text-muted-foreground">Loading form data...</div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
      {/* Basic Information */}
      <Card>
        <CardHeader className="bg-primary text-white">
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Service Report Information
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6 space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Customer Selection */}
            <div className="space-y-2">
              <Label htmlFor="customerId">Customer *</Label>
              <Select
                value={form.watch('customerId')}
                onValueChange={(value) => form.setValue('customerId', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select customer" />
                </SelectTrigger>
                <SelectContent>
                  {customers.map((customer) => (
                    <SelectItem key={customer.id} value={customer.id}>
                      {customer.name} - {customer.city}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {form.formState.errors.customerId && (
                <p className="text-sm text-destructive">
                  {form.formState.errors.customerId.message}
                </p>
              )}
            </div>

            {/* Executive Selection */}
            <div className="space-y-2">
              <Label htmlFor="executiveId">Executive *</Label>
              <Select
                value={form.watch('executiveId')}
                onValueChange={(value) => form.setValue('executiveId', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select executive" />
                </SelectTrigger>
                <SelectContent>
                  {executives.map((executive) => (
                    <SelectItem key={executive.id} value={executive.id}>
                      {executive.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {form.formState.errors.executiveId && (
                <p className="text-sm text-destructive">
                  {form.formState.errors.executiveId.message}
                </p>
              )}
            </div>

            {/* Report Date */}
            <div className="space-y-2">
              <Label>Report Date *</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      'w-full justify-start text-left font-normal',
                      !form.watch('reportDate') && 'text-muted-foreground'
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {form.watch('reportDate') ? (
                      format(form.watch('reportDate'), 'PPP')
                    ) : (
                      <span>Pick a date</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={form.watch('reportDate')}
                    onSelect={(date) => form.setValue('reportDate', date || new Date())}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              {form.formState.errors.reportDate && (
                <p className="text-sm text-destructive">
                  {form.formState.errors.reportDate.message}
                </p>
              )}
            </div>

            {/* Visit Date */}
            <div className="space-y-2">
              <Label>Visit Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      'w-full justify-start text-left font-normal',
                      !form.watch('visitDate') && 'text-muted-foreground'
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {form.watch('visitDate') ? (
                      format(form.watch('visitDate'), 'PPP')
                    ) : (
                      <span>Pick a date</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={form.watch('visitDate')}
                    onSelect={(date) => form.setValue('visitDate', date)}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              {form.formState.errors.visitDate && (
                <p className="text-sm text-destructive">
                  {form.formState.errors.visitDate.message}
                </p>
              )}
            </div>

            {/* Nature of Service */}
            <div className="space-y-2">
              <Label htmlFor="natureOfService">Nature of Service *</Label>
              <Input
                id="natureOfService"
                {...form.register('natureOfService')}
                placeholder="Enter nature of service"
              />
              {form.formState.errors.natureOfService && (
                <p className="text-sm text-destructive">
                  {form.formState.errors.natureOfService.message}
                </p>
              )}
            </div>

            {/* Complaint Type */}
            <div className="space-y-2">
              <Label htmlFor="complaintType">Complaint Type *</Label>
              <Select
                value={form.watch('complaintType')}
                onValueChange={(value) => form.setValue('complaintType', value as any)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select complaint type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="REPAIR">Repair</SelectItem>
                  <SelectItem value="MAINTENANCE">Maintenance</SelectItem>
                  <SelectItem value="INSTALLATION">Installation</SelectItem>
                  <SelectItem value="INSPECTION">Inspection</SelectItem>
                  <SelectItem value="WARRANTY">Warranty</SelectItem>
                  <SelectItem value="OTHER">Other</SelectItem>
                </SelectContent>
              </Select>
              {form.formState.errors.complaintType && (
                <p className="text-sm text-destructive">
                  {form.formState.errors.complaintType.message}
                </p>
              )}
            </div>
          </div>

          {/* Action Taken */}
          <div className="space-y-2">
            <Label htmlFor="actionTaken">Action Taken</Label>
            <Textarea
              id="actionTaken"
              {...form.register('actionTaken')}
              placeholder="Describe the action taken"
              rows={3}
            />
            {form.formState.errors.actionTaken && (
              <p className="text-sm text-destructive">
                {form.formState.errors.actionTaken.message}
              </p>
            )}
          </div>

          {/* Remarks */}
          <div className="space-y-2">
            <Label htmlFor="remarks">Remarks</Label>
            <Textarea
              id="remarks"
              {...form.register('remarks')}
              placeholder="Additional remarks"
              rows={3}
            />
            {form.formState.errors.remarks && (
              <p className="text-sm text-destructive">
                {form.formState.errors.remarks.message}
              </p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Service Details */}
      <Card>
        <CardHeader className="bg-primary text-white">
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Service Details
            </span>
            <Button
              type="button"
              variant="secondary"
              size="sm"
              onClick={addServiceDetail}
              className="bg-white text-primary hover:bg-gray-100"
            >
              <Plus className="h-4 w-4 mr-1" />
              Add Detail
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="space-y-4">
            {fields.map((field, index) => (
              <div key={field.id} className="border rounded-lg p-4 space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">Service Detail {index + 1}</h4>
                  {fields.length > 1 && (
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => removeServiceDetail(index)}
                      className="text-destructive hover:text-destructive"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor={`details.${index}.machineType`}>Machine Type *</Label>
                    <Input
                      {...form.register(`details.${index}.machineType`)}
                      placeholder="Enter machine type"
                    />
                    {form.formState.errors.details?.[index]?.machineType && (
                      <p className="text-sm text-destructive">
                        {form.formState.errors.details[index]?.machineType?.message}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor={`details.${index}.serialNumber`}>Serial Number *</Label>
                    <Input
                      {...form.register(`details.${index}.serialNumber`)}
                      placeholder="Enter serial number"
                    />
                    {form.formState.errors.details?.[index]?.serialNumber && (
                      <p className="text-sm text-destructive">
                        {form.formState.errors.details[index]?.serialNumber?.message}
                      </p>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor={`details.${index}.problem`}>Problem *</Label>
                  <Textarea
                    {...form.register(`details.${index}.problem`)}
                    placeholder="Describe the problem"
                    rows={2}
                  />
                  {form.formState.errors.details?.[index]?.problem && (
                    <p className="text-sm text-destructive">
                      {form.formState.errors.details[index]?.problem?.message}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor={`details.${index}.solution`}>Solution *</Label>
                  <Textarea
                    {...form.register(`details.${index}.solution`)}
                    placeholder="Describe the solution"
                    rows={2}
                  />
                  {form.formState.errors.details?.[index]?.solution && (
                    <p className="text-sm text-destructive">
                      {form.formState.errors.details[index]?.solution?.message}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor={`details.${index}.partReplaced`}>Part Replaced</Label>
                  <Input
                    {...form.register(`details.${index}.partReplaced`)}
                    placeholder="Enter part replaced (if any)"
                  />
                  {form.formState.errors.details?.[index]?.partReplaced && (
                    <p className="text-sm text-destructive">
                      {form.formState.errors.details[index]?.partReplaced?.message}
                    </p>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Form Actions */}
      <div className="flex items-center justify-end gap-4">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isLoading}
        >
          <X className="h-4 w-4 mr-2" />
          Cancel
        </Button>
        <Button
          type="submit"
          disabled={isLoading}
          className="bg-primary text-white hover:bg-primary/90"
        >
          {isLoading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
              Saving...
            </>
          ) : (
            <>
              <Save className="h-4 w-4 mr-2" />
              Save Service Report
            </>
          )}
        </Button>
      </div>

      {/* Form Errors */}
      {Object.keys(form.formState.errors).length > 0 && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Please correct the errors above before submitting.
          </AlertDescription>
        </Alert>
      )}
    </form>
  );
}
