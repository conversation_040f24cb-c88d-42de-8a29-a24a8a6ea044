'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarIcon, Loader2, AlertCircle } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { toast } from '@/components/ui/use-toast';
import { Alert, AlertDescription } from '@/components/ui/alert';

// Helper function to validate UUID format
const isValidUUID = (value: string | null | undefined): value is string => {
  if (!value || typeof value !== 'string') return false;
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(value);
};

// Validation schema for warranty to out-warranty conversion
const warrantyToOutWarrantyConversionSchema = z.object({
  reason: z.string().min(1, 'Conversion reason is required').max(500),
  effectiveDate: z.date({ required_error: 'Effective date is required' }),
  notes: z.string().max(1000).optional(),
  outWarrantyData: z.object({
    startDate: z.date({ required_error: 'Out-warranty start date is required' }),
    endDate: z.date({ required_error: 'Out-warranty end date is required' }),
    executiveId: z.string().uuid().optional(),
    contactPersonId: z.string().uuid().optional(),
    technicianId: z.string().uuid().optional(),
  }).refine(
    (data) => data.endDate > data.startDate,
    {
      message: 'Out-warranty end date must be after start date',
      path: ['endDate'],
    }
  ),
}).refine(
  (data) => data.outWarrantyData.startDate >= data.effectiveDate,
  {
    message: 'Out-warranty start date must be on or after effective date',
    path: ['outWarrantyData', 'startDate'],
  }
);

type WarrantyToOutWarrantyConversionForm = z.infer<typeof warrantyToOutWarrantyConversionSchema>;

interface WarrantyDetails {
  id: string;
  customerId: string;
  customerName: string;
  bslNo?: string | null;
  numberOfMachines: number;
  executiveId?: string | null;
  contactPersonId?: string | null;
}

interface WarrantyToOutWarrantyConversionDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  warrantyId: string;
  warrantyDetails?: WarrantyDetails;
  onSuccess?: (result: any) => void;
}

export function WarrantyToOutWarrantyConversionDialog({
  open,
  onOpenChange,
  warrantyId,
  warrantyDetails,
  onSuccess,
}: WarrantyToOutWarrantyConversionDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<WarrantyToOutWarrantyConversionForm>({
    resolver: zodResolver(warrantyToOutWarrantyConversionSchema),
    defaultValues: {
      reason: '',
      effectiveDate: new Date(),
      notes: '',
      outWarrantyData: {
        startDate: new Date(),
        endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
        executiveId: isValidUUID(warrantyDetails?.executiveId) ? warrantyDetails.executiveId : undefined,
        contactPersonId: isValidUUID(warrantyDetails?.contactPersonId) ? warrantyDetails.contactPersonId : undefined,
        technicianId: undefined,
      },
    },
  });

  const onSubmit = async (data: WarrantyToOutWarrantyConversionForm) => {
    setIsSubmitting(true);
    try {
      const response = await fetch('/api/conversions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          sourceId: warrantyId,
          conversionType: 'WARRANTY_TO_OUT_WARRANTY',
          reason: data.reason,
          effectiveDate: data.effectiveDate.toISOString(),
          notes: data.notes,
          outWarrantyData: {
            startDate: data.outWarrantyData.startDate.toISOString(),
            endDate: data.outWarrantyData.endDate.toISOString(),
            executiveId: data.outWarrantyData.executiveId,
            contactPersonId: data.outWarrantyData.contactPersonId,
            technicianId: data.outWarrantyData.technicianId,
          },
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to convert warranty to out-warranty');
      }

      const result = await response.json();

      toast({
        title: 'Conversion Successful',
        description: `Warranty has been successfully converted to out-warranty record ${result.targetId}`,
      });

      onOpenChange(false);
      form.reset();

      if (onSuccess) {
        onSuccess(result);
      }
    } catch (error: any) {
      toast({
        title: 'Conversion Failed',
        description: error.message || 'Failed to convert warranty to out-warranty',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-primary">Convert Warranty to Out-of-Warranty</DialogTitle>
          <DialogDescription>
            Convert this warranty record to an out-of-warranty service record. This action will mark the warranty as converted and create a new out-of-warranty record.
          </DialogDescription>
        </DialogHeader>

        {warrantyDetails && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <strong>Warranty Details:</strong><br />
              Customer: {warrantyDetails.customerName}<br />
              {warrantyDetails.bslNo && `BSL No: ${warrantyDetails.bslNo}`}<br />
              Machines: {warrantyDetails.numberOfMachines}
            </AlertDescription>
          </Alert>
        )}

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="reason"
                render={({ field }) => (
                  <FormItem className="md:col-span-2">
                    <FormLabel>Conversion Reason *</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter the reason for converting this warranty to out-of-warranty..."
                        className="min-h-[80px]"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Explain why this warranty is being converted to out-of-warranty status
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="effectiveDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Effective Date *</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              'w-full pl-3 text-left font-normal',
                              !field.value && 'text-muted-foreground'
                            )}
                          >
                            {field.value ? (
                              format(field.value, 'PPP')
                            ) : (
                              <span>Pick effective date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) => date < new Date('1900-01-01')}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormDescription>
                      Date when the conversion takes effect
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="outWarrantyData.startDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Out-Warranty Start Date *</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              'w-full pl-3 text-left font-normal',
                              !field.value && 'text-muted-foreground'
                            )}
                          >
                            {field.value ? (
                              format(field.value, 'PPP')
                            ) : (
                              <span>Pick start date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) => date < new Date('1900-01-01')}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="outWarrantyData.endDate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Out-Warranty End Date *</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant="outline"
                          className={cn(
                            'w-full pl-3 text-left font-normal',
                            !field.value && 'text-muted-foreground'
                          )}
                        >
                          {field.value ? (
                            format(field.value, 'PPP')
                          ) : (
                            <span>Pick end date</span>
                          )}
                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={field.value}
                        onSelect={field.onChange}
                        disabled={(date) => date < new Date('1900-01-01')}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Additional Notes</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter any additional notes about this conversion..."
                      className="min-h-[80px]"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Optional notes that will be included in the conversion record
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Convert to Out-Warranty
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
