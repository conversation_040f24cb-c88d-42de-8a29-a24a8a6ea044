import { NextRequest, NextResponse } from 'next/server';
import { getCustomerRepository } from '@/lib/repositories';
import { z } from 'zod';
import { withRoleProtection } from '@/lib/auth/role-check';
import { ActivityLoggerMiddlewareFactory } from '@/lib/middleware/activity-logger.middleware';

/**
 * Customer search schema
 */
const searchCustomerSchema = z.object({
  name: z.string().optional(),
  email: z.string().optional(),
  phone: z.string().optional(),
  mobile: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  pinCode: z.string().optional(),
  location: z.string().optional(),
  isActive: z.enum(['true', 'false', 'all']).optional(), // Added 'all' as a valid value
  segment: z.string().optional(),
  createdAfter: z.string().optional(),
  createdBefore: z.string().optional(),
  searchAll: z.string().optional(), // New field for searching across all text fields
  skip: z.coerce.number().default(0),
  take: z.coerce.number().default(10),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).default('asc'),
});

/**
 * GET /api/customers/search
 * Advanced search for customers with multiple criteria
 */
async function searchCustomers(request: NextRequest) {
  try {
    // Parse query parameters
    const url = new URL(request.url);
    const params = Object.fromEntries(url.searchParams.entries());

    console.log('Customer search params:', params);

    // Validate query parameters
    try {
      const validatedParams = searchCustomerSchema.parse(params);

      const {
        name,
        email,
        phone,
        mobile,
        city,
        state,
        pinCode,
        location,
        isActive,
        segment,
        createdAfter,
        createdBefore,
        searchAll,
        skip,
        take,
        sortBy,
        sortOrder,
      } = validatedParams;

      const customerRepository = getCustomerRepository();

      // Build where clause for Prisma
      const where: any = {};

      // Handle global search across multiple fields
      if (searchAll) {
        where.OR = [
          { name: { contains: searchAll, mode: 'insensitive' } },
          { email: { contains: searchAll, mode: 'insensitive' } },
          { phone: { contains: searchAll, mode: 'insensitive' } },
          { phone1: { contains: searchAll, mode: 'insensitive' } },
          { phone2: { contains: searchAll, mode: 'insensitive' } },
          { phone3: { contains: searchAll, mode: 'insensitive' } },
          { mobile: { contains: searchAll, mode: 'insensitive' } },
          { city: { contains: searchAll, mode: 'insensitive' } },
          { state: { contains: searchAll, mode: 'insensitive' } },
          { pinCode: { contains: searchAll, mode: 'insensitive' } },
          { location: { contains: searchAll, mode: 'insensitive' } },
        ];
      } else {
        // Handle individual field searches
        if (name) {
          where.name = {
            contains: name,
            mode: 'insensitive',
          };
        }

        if (email) {
          where.email = {
            contains: email,
            mode: 'insensitive',
          };
        }

        if (phone) {
          where.OR = [
            { phone: { contains: phone, mode: 'insensitive' } },
            { phone1: { contains: phone, mode: 'insensitive' } },
            { phone2: { contains: phone, mode: 'insensitive' } },
            { phone3: { contains: phone, mode: 'insensitive' } },
          ];
        }

        if (mobile) {
          where.mobile = {
            contains: mobile,
            mode: 'insensitive',
          };
        }

        if (city) {
          where.city = {
            contains: city,
            mode: 'insensitive',
          };
        }

        if (state) {
          where.state = {
            contains: state,
            mode: 'insensitive',
          };
        }

        if (pinCode) {
          where.pinCode = {
            contains: pinCode,
            mode: 'insensitive',
          };
        }

        if (location) {
          where.location = {
            contains: location,
            mode: 'insensitive',
          };
        }
      }

      // Handle status filter
      if (isActive !== undefined && isActive !== 'all') {
        where.isActive = isActive === 'true';
      }

      // Handle segment filter
      if (segment) {
        where.segmentId = segment;
      }

      // Handle date range filters
      if (createdAfter || createdBefore) {
        where.createdAt = {};

        if (createdAfter) {
          where.createdAt.gte = new Date(createdAfter);
        }

        if (createdBefore) {
          where.createdAt.lte = new Date(createdBefore);
        }
      }

      // Build orderBy clause for Prisma
      const orderBy: any = {};
      if (sortBy) {
        orderBy[sortBy] = sortOrder;
      } else {
        orderBy.name = 'asc';
      }

      console.log('Search where clause:', JSON.stringify(where));
      console.log('Search orderBy:', orderBy);

      // Get customers with pagination and filtering
      const [customers, total] = await Promise.all([
        customerRepository.findBy(where, skip, take, orderBy),
        customerRepository.count(where),
      ]);

      return NextResponse.json({
        success: true,
        data: customers,
        meta: {
          total,
          skip,
          take,
        },
      });
    } catch (validationError) {
      console.error('Validation error:', validationError);
      if (validationError instanceof z.ZodError) {
        return NextResponse.json(
          { error: 'Validation error', details: validationError.errors },
          { status: 400 }
        );
      }
      throw validationError; // Re-throw if it's not a ZodError
    }
  } catch (error) {
    console.error('Error searching customers:', error);
    return NextResponse.json(
      { error: 'Failed to search customers' },
      { status: 500 }
    );
  }
}

// Export handler with role protection and activity logging
export const GET = ActivityLoggerMiddlewareFactory.forCustomerRoutes(
  withRoleProtection(searchCustomers, ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER']),
  {
    action: 'search_customers',
    getEntityId: () => null, // No specific entity ID for search
  }
);
