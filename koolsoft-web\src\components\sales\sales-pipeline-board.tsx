'use client';

import React, { useState, useEffect, useCallback } from 'react';
import {
  DndContext,
  DragEndEvent,
  DragOverEvent,
  DragStartEvent,
  PointerSensor,
  useSensor,
  useSensors,
  closestCorners,
} from '@dnd-kit/core';
import { arrayMove } from '@dnd-kit/sortable';
import { PipelineColumn } from './pipeline-column';
import { PipelineCard } from './pipeline-card';
import { useToast } from '@/components/ui/use-toast';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle } from 'lucide-react';

// Types
export interface SalesItem {
  id: string;
  type: 'lead' | 'opportunity' | 'prospect' | 'order';
  customerId: string;
  executiveId: string;
  status: string;
  amount?: number;
  contactPerson?: string;
  contactPhone?: string;
  prospectPercentage?: number;
  followUpDate?: string;
  nextVisitDate?: string;
  remarks?: string;
  createdAt: string;
  updatedAt: string;
  customer: {
    id: string;
    name: string;
    email?: string;
    phone?: string;
    city?: string;
  };
  executive: {
    id: string;
    name: string;
    email?: string;
    designation?: string;
  };
}

// Pipeline columns configuration
const PIPELINE_COLUMNS = [
  {
    id: 'NEW',
    title: 'New',
    color: 'bg-blue-100 border-blue-200',
    headerColor: 'bg-blue-500 text-white',
  },
  {
    id: 'CONTACTED',
    title: 'Contacted',
    color: 'bg-yellow-100 border-yellow-200',
    headerColor: 'bg-yellow-500 text-white',
  },
  {
    id: 'QUALIFIED',
    title: 'Qualified',
    color: 'bg-green-100 border-green-200',
    headerColor: 'bg-green-500 text-white',
  },
  {
    id: 'PROPOSAL',
    title: 'Proposal',
    color: 'bg-purple-100 border-purple-200',
    headerColor: 'bg-purple-500 text-white',
  },
  {
    id: 'NEGOTIATION',
    title: 'Negotiation',
    color: 'bg-orange-100 border-orange-200',
    headerColor: 'bg-orange-500 text-white',
  },
  {
    id: 'CLOSED_WON',
    title: 'Closed Won',
    color: 'bg-emerald-100 border-emerald-200',
    headerColor: 'bg-emerald-500 text-white',
  },
  {
    id: 'CLOSED_LOST',
    title: 'Closed Lost',
    color: 'bg-red-100 border-red-200',
    headerColor: 'bg-red-500 text-white',
  },
];

export function SalesPipelineBoard() {
  const { toast } = useToast();
  const [salesItems, setSalesItems] = useState<SalesItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeId, setActiveId] = useState<string | null>(null);

  // Configure sensors for drag and drop
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  );

  // Fetch sales data from all endpoints
  const fetchSalesData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch data from all sales endpoints
      const [leadsResponse, opportunitiesResponse, prospectsResponse, ordersResponse] = await Promise.all([
        fetch('/api/sales/leads?take=100', { credentials: 'include' }),
        fetch('/api/sales/opportunities?take=100', { credentials: 'include' }),
        fetch('/api/sales/prospects?take=100', { credentials: 'include' }),
        fetch('/api/sales/orders?take=100', { credentials: 'include' }),
      ]);

      if (!leadsResponse.ok || !opportunitiesResponse.ok || !prospectsResponse.ok || !ordersResponse.ok) {
        throw new Error('Failed to fetch sales data');
      }

      const [leadsData, opportunitiesData, prospectsData, ordersData] = await Promise.all([
        leadsResponse.json(),
        opportunitiesResponse.json(),
        prospectsResponse.json(),
        ordersResponse.json(),
      ]);

      // Combine all sales items with type information
      const allItems: SalesItem[] = [
        ...(leadsData.data || []).map((item: any) => ({ ...item, type: 'lead' as const })),
        ...(opportunitiesData.data || []).map((item: any) => ({ ...item, type: 'opportunity' as const })),
        ...(prospectsData.data || []).map((item: any) => ({ ...item, type: 'prospect' as const })),
        ...(ordersData.data || []).map((item: any) => ({ ...item, type: 'order' as const })),
      ];

      setSalesItems(allItems);
    } catch (error) {
      console.error('Error fetching sales data:', error);
      setError('Failed to load sales data. Please try again.');
      toast({
        title: 'Error',
        description: 'Failed to load sales data. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  }, [toast]);

  // Load data on component mount
  useEffect(() => {
    fetchSalesData();
  }, [fetchSalesData]);

  // Handle drag start
  const handleDragStart = (event: DragStartEvent) => {
    setActiveId(event.active.id as string);
  };

  // Handle drag end
  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;
    setActiveId(null);

    if (!over) return;

    const activeId = active.id as string;
    const overId = over.id as string;

    // Find the active item
    const activeItem = salesItems.find(item => item.id === activeId);
    if (!activeItem) return;

    // Check if we're dropping on a column
    const targetColumn = PIPELINE_COLUMNS.find(col => col.id === overId);
    if (!targetColumn) return;

    // If the status is already the same, no need to update
    if (activeItem.status === targetColumn.id) return;

    try {
      // Optimistically update the UI
      setSalesItems(items =>
        items.map(item =>
          item.id === activeId
            ? { ...item, status: targetColumn.id }
            : item
        )
      );

      // Update the item status via API
      const endpoint = `/api/sales/${activeItem.type === 'lead' ? 'leads' : 
                                   activeItem.type === 'opportunity' ? 'opportunities' :
                                   activeItem.type === 'prospect' ? 'prospects' : 'orders'}/${activeId}`;

      const response = await fetch(endpoint, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          status: targetColumn.id,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update status');
      }

      toast({
        title: 'Success',
        description: `${activeItem.type} status updated to ${targetColumn.title}`,
      });
    } catch (error) {
      console.error('Error updating status:', error);
      
      // Revert the optimistic update
      setSalesItems(items =>
        items.map(item =>
          item.id === activeId
            ? { ...item, status: activeItem.status }
            : item
        )
      );

      toast({
        title: 'Error',
        description: 'Failed to update status. Please try again.',
        variant: 'destructive',
      });
    }
  };

  // Group items by status
  const itemsByStatus = salesItems.reduce((acc, item) => {
    if (!acc[item.status]) {
      acc[item.status] = [];
    }
    acc[item.status].push(item);
    return acc;
  }, {} as Record<string, SalesItem[]>);

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-7 gap-4">
        {PIPELINE_COLUMNS.map((column) => (
          <div key={column.id} className="space-y-4">
            <Skeleton className="h-12 w-full" />
            <Skeleton className="h-32 w-full" />
            <Skeleton className="h-32 w-full" />
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCorners}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-7 gap-4 min-h-[600px]">
        {PIPELINE_COLUMNS.map((column) => (
          <PipelineColumn
            key={column.id}
            id={column.id}
            title={column.title}
            color={column.color}
            headerColor={column.headerColor}
            items={itemsByStatus[column.id] || []}
            activeId={activeId}
          />
        ))}
      </div>
    </DndContext>
  );
}
