import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { hash } from 'bcrypt';
import { withAdminProtection } from '@/lib/auth/role-check';

/**
 * GET /api/users
 *
 * Get a list of users with pagination
 * Accessible to all authenticated users
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse query parameters
    const url = new URL(req.url);
    const skip = parseInt(url.searchParams.get('skip') || '0');
    const take = parseInt(url.searchParams.get('take') || '100');
    const rolesParam = url.searchParams.get('roles');

    // Build where clause
    const where: any = {
      isActive: true, // Only return active users
    };

    // Filter by roles if provided
    if (rolesParam) {
      const roles = rolesParam.split(',').map(role => role.trim());
      where.role = {
        in: roles,
      };
    }

    // Get users with pagination
    const [users, total] = await Promise.all([
      prisma.users.findMany({
        where,
        skip,
        take,
        orderBy: { name: 'asc' },
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          phone: true,
          designation: true,
        },
      }),
      prisma.users.count({
        where,
      }),
    ]);

    // Return the results in the expected format
    return NextResponse.json({
      success: true,
      data: {
        users,
      },
      meta: {
        total,
        skip,
        take,
      },
    });
  } catch (error) {
    console.error('Error fetching users:', error);
    return NextResponse.json(
      { error: 'Failed to fetch users' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/users
 *
 * Create a new user
 * Only accessible to admin users
 */
async function createUser(req: NextRequest) {
  try {
    // Parse request body
    const body = await req.json();

    // Validate request body
    const userSchema = z.object({
      name: z.string().min(2).max(100),
      email: z.string().email(),
      password: z.string().min(8),
      role: z.enum(['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER']),
      phone: z.string().optional(),
      designation: z.string().optional(),
      isActive: z.boolean().default(true),
    });

    const { name, email, password, role, phone, designation, isActive } = userSchema.parse(body);

    // Check if user with email already exists
    const existingUser = await prisma.users.findUnique({
      where: { email },
    });

    if (existingUser) {
      return NextResponse.json(
        { error: 'User with this email already exists' },
        { status: 400 }
      );
    }

    // Hash the password before storing it
    const hashedPassword = await hash(password, 10);

    // Create the user
    const user = await prisma.users.create({
      data: {
        name,
        email,
        password: hashedPassword,
        role,
        phone,
        designation,
        isActive,
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        isActive: true,
        createdAt: true,
      },
    });

    return NextResponse.json(user, { status: 201 });
  } catch (error) {
    console.error('Error creating user:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to create user' },
      { status: 500 }
    );
  }
}

// Export the handler with admin protection
export const POST = withAdminProtection(createUser);
