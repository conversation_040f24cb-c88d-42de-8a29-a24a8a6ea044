const https = require('http');

async function testAPIHTTP() {
  try {
    console.log('🌐 Testing Out-Warranty API via HTTP...\n');
    
    // Test different API calls
    const testCases = [
      { url: 'http://localhost:3001/api/out-warranties', desc: 'Default call (no params)' },
      { url: 'http://localhost:3001/api/out-warranties?isActive=true', desc: 'With isActive=true' },
      { url: 'http://localhost:3001/api/out-warranties?isActive=false', desc: 'With isActive=false' },
      { url: 'http://localhost:3001/api/out-warranties?take=10', desc: 'With take=10' },
    ];
    
    for (const testCase of testCases) {
      console.log(`📋 Testing: ${testCase.desc}`);
      console.log(`🔗 URL: ${testCase.url}`);
      
      try {
        const response = await fetch(testCase.url, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            // Note: This won't work without authentication, but let's see the response
          }
        });
        
        console.log(`📊 Status: ${response.status} ${response.statusText}`);
        
        if (response.ok) {
          const data = await response.json();
          console.log(`📦 Response data:`, JSON.stringify(data, null, 2));
        } else {
          const errorText = await response.text();
          console.log(`❌ Error response:`, errorText);
        }
        
      } catch (error) {
        console.error(`❌ Request failed:`, error.message);
      }
      
      console.log('---\n');
    }
    
  } catch (error) {
    console.error('❌ Error testing API:', error);
  }
}

testAPIHTTP();
