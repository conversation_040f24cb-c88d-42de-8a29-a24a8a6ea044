import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { ConversionReportRepository } from '@/lib/repositories/conversion-report.repository';
import { conversionExportSchema } from '@/lib/validations/conversion.schema';
import { logActivity } from '@/lib/activity-logger';
import { format } from 'date-fns';

/**
 * POST /api/conversions/reports/export
 * Export conversion reports in various formats
 */
async function exportHandler(request: NextRequest) {
  try {
    const body = await request.json();
    const validatedParams = conversionExportSchema.parse(body);
    
    // Get conversion data
    const repository = new ConversionReportRepository();
    const result = await repository.getDetailedConversions({
      ...validatedParams.filters,
      page: 1,
      limit: 10000, // Large number to get all records for export
    });

    // Generate export based on format
    switch (validatedParams.format) {
      case 'CSV':
        return generateCSVExport(result.conversions, validatedParams);
      case 'EXCEL':
        return generateExcelExport(result.conversions, validatedParams);
      case 'PDF':
        return generatePDFExport(result.conversions, validatedParams);
      default:
        return NextResponse.json(
          { error: 'Unsupported export format' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Error exporting conversion reports:', error);
    return NextResponse.json(
      { error: 'Failed to export conversion reports' },
      { status: 500 }
    );
  }
}

/**
 * Generate CSV export
 */
function generateCSVExport(conversions: any[], params: any) {
  const headers = [
    'Conversion ID',
    'Card Number',
    'Conversion Type',
    'Customer Name',
    'Customer City',
    'Source ID',
    'Target ID',
    'Amount',
    'Conversion Date',
    'Status',
  ];

  if (params.includeDetails) {
    headers.push(
      'Source Details',
      'Target Details',
      'Notes'
    );
  }

  const csvRows = [headers.join(',')];

  conversions.forEach(conversion => {
    const row = [
      conversion.id,
      conversion.cardNo || '',
      conversion.source || '',
      `"${conversion.customer?.name || ''}"`,
      `"${conversion.customer?.city || ''}"`,
      conversion.inWarrantyId || conversion.amcId || '',
      conversion.amcId || conversion.outWarrantyId || '',
      getConversionAmount(conversion),
      format(new Date(conversion.createdAt), 'yyyy-MM-dd HH:mm:ss'),
      'SUCCESS', // All conversions in history_cards are successful
    ];

    if (params.includeDetails) {
      row.push(
        `"${getSourceDetails(conversion)}"`,
        `"${getTargetDetails(conversion)}"`,
        `"${conversion.notes || ''}"`
      );
    }

    csvRows.push(row.join(','));
  });

  const csvContent = csvRows.join('\n');
  const now = format(new Date(), 'yyyy-MM-dd_HHmmss');
  const filename = `conversion_reports_${now}.csv`;

  // Log the export activity
  logActivity({
    action: 'EXPORT',
    entityType: 'CONVERSION_REPORT',
    details: `Exported ${conversions.length} conversion records in CSV format`,
    request: null as any
  });

  return new NextResponse(csvContent, {
    headers: {
      'Content-Type': 'text/csv',
      'Content-Disposition': `attachment; filename="${filename}"`,
    },
  });
}

/**
 * Generate Excel export (placeholder - returns CSV with Excel MIME type)
 */
function generateExcelExport(conversions: any[], params: any) {
  const csvResponse = generateCSVExport(conversions, params);
  
  const now = format(new Date(), 'yyyy-MM-dd_HHmmss');
  const filename = `conversion_reports_${now}.xlsx`;

  return new NextResponse(csvResponse.body, {
    headers: {
      'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'Content-Disposition': `attachment; filename="${filename}"`,
    },
  });
}

/**
 * Generate PDF export (placeholder)
 */
function generatePDFExport(conversions: any[], params: any) {
  const textContent = conversions.map(conversion => 
    `Conversion ID: ${conversion.id}\n` +
    `Type: ${conversion.source}\n` +
    `Customer: ${conversion.customer?.name}\n` +
    `Amount: ${getConversionAmount(conversion)}\n` +
    `Date: ${format(new Date(conversion.createdAt), 'yyyy-MM-dd')}\n\n`
  ).join('');

  const now = format(new Date(), 'yyyy-MM-dd_HHmmss');
  const filename = `conversion_reports_${now}.pdf`;

  return new NextResponse(textContent, {
    headers: {
      'Content-Type': 'application/pdf',
      'Content-Disposition': `attachment; filename="${filename}"`,
    },
  });
}

/**
 * Get conversion amount based on type
 */
function getConversionAmount(conversion: any): string {
  if (conversion.amcContract?.amount) {
    return conversion.amcContract.amount.toString();
  }
  if (conversion.outWarranty?.amount) {
    return conversion.outWarranty.amount.toString();
  }
  return '0';
}

/**
 * Get source details
 */
function getSourceDetails(conversion: any): string {
  if (conversion.inWarranty) {
    return `BSL No: ${conversion.inWarranty.bslNo}, Install Date: ${conversion.inWarranty.installDate}`;
  }
  if (conversion.amcContract) {
    return `Contract ID: ${conversion.amcContract.id}, Period: ${conversion.amcContract.startDate} to ${conversion.amcContract.endDate}`;
  }
  return '';
}

/**
 * Get target details
 */
function getTargetDetails(conversion: any): string {
  if (conversion.amcContract) {
    return `Contract ID: ${conversion.amcContract.id}, Amount: ${conversion.amcContract.amount}`;
  }
  if (conversion.outWarranty) {
    return `Period: ${conversion.outWarranty.startDate} to ${conversion.outWarranty.endDate}, Amount: ${conversion.outWarranty.amount}`;
  }
  return '';
}

export const POST = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  exportHandler
);
