'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/components/ui/use-toast';
import { DataTable } from '@/components/ui/data-table';
import { ColumnDef } from '@tanstack/react-table';
import { format } from 'date-fns';
import { 
  Plus, 
  Search, 
  Filter, 
  Download, 
  Eye, 
  Edit, 
  Trash2,
  Calendar,
  User,
  Phone,
  Mail,
  Building,
  MoreHorizontal
} from 'lucide-react';
import Link from 'next/link';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';

// Types
interface Lead {
  id: string;
  customerId: string;
  executiveId: string;
  leadDate: string;
  contactPerson?: string;
  contactPhone?: string;
  status: string;
  prospectPercentage?: number;
  followUpDate?: string;
  nextVisitDate?: string;
  remarks?: string;
  createdAt: string;
  updatedAt: string;
  customer: {
    id: string;
    name: string;
    email?: string;
    phone?: string;
    city?: string;
  };
  executive: {
    id: string;
    name: string;
    email?: string;
    designation?: string;
  };
}

interface LeadsResponse {
  success: boolean;
  data: {
    leads: Lead[];
    total: number;
    page: number;
    totalPages: number;
  };
}

// Status options
const statusOptions = [
  { value: 'all', label: 'All Statuses' },
  { value: 'NEW', label: 'New' },
  { value: 'CONTACTED', label: 'Contacted' },
  { value: 'QUALIFIED', label: 'Qualified' },
  { value: 'PROPOSAL', label: 'Proposal' },
  { value: 'NEGOTIATION', label: 'Negotiation' },
  { value: 'CLOSED_WON', label: 'Closed Won' },
  { value: 'CLOSED_LOST', label: 'Closed Lost' },
];

// Status badge colors
const getStatusColor = (status: string) => {
  switch (status) {
    case 'NEW': return 'bg-blue-100 text-blue-800';
    case 'CONTACTED': return 'bg-yellow-100 text-yellow-800';
    case 'QUALIFIED': return 'bg-green-100 text-green-800';
    case 'PROPOSAL': return 'bg-purple-100 text-purple-800';
    case 'NEGOTIATION': return 'bg-orange-100 text-orange-800';
    case 'CLOSED_WON': return 'bg-green-100 text-green-800';
    case 'CLOSED_LOST': return 'bg-red-100 text-red-800';
    default: return 'bg-gray-100 text-gray-800';
  }
};

export default function LeadsPage() {
  const { toast } = useToast();
  const [leads, setLeads] = useState<Lead[]>([]);
  const [loading, setLoading] = useState(true);
  const [totalCount, setTotalCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // Filter states
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');

  // Bulk operations states
  const [selectedLeads, setSelectedLeads] = useState<string[]>([]);
  const [bulkLoading, setBulkLoading] = useState(false);

  // Fetch leads data
  const fetchLeads = useCallback(async () => {
    try {
      setLoading(true);
      
      const params = new URLSearchParams({
        skip: ((currentPage - 1) * pageSize).toString(),
        take: pageSize.toString(),
        sortBy: 'leadDate',
        sortOrder: 'desc',
      });

      if (searchTerm) params.append('search', searchTerm);
      if (statusFilter && statusFilter !== 'all') params.append('status', statusFilter);
      if (startDate) params.append('startDate', startDate);
      if (endDate) params.append('endDate', endDate);

      const response = await fetch(`/api/sales/leads?${params.toString()}`, {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to fetch leads');
      }

      const result = await response.json();

      if (result.success) {
        setLeads(result.data || []);
        setTotalCount(result.pagination?.total || 0);
        setTotalPages(result.pagination?.pages || 1);
      } else {
        throw new Error('Failed to load leads');
      }
    } catch (error) {
      console.error('Error fetching leads:', error);
      toast({
        title: 'Error',
        description: 'Failed to load leads. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  }, [currentPage, pageSize, searchTerm, statusFilter, startDate, endDate, toast]);

  // Load leads on component mount and when filters change
  useEffect(() => {
    fetchLeads();
  }, [fetchLeads]);

  // Handle search
  const handleSearch = () => {
    setCurrentPage(1);
    fetchLeads();
  };

  // Clear filters
  const clearFilters = () => {
    setSearchTerm('');
    setStatusFilter('all');
    setStartDate('');
    setEndDate('');
    setCurrentPage(1);
  };

  // Export leads
  const handleExport = async () => {
    try {
      const params = new URLSearchParams();
      if (searchTerm) params.append('search', searchTerm);
      if (statusFilter && statusFilter !== 'all') params.append('status', statusFilter);
      if (startDate) params.append('startDate', startDate);
      if (endDate) params.append('endDate', endDate);
      params.append('format', 'CSV');

      const response = await fetch(`/api/sales/leads/export?${params.toString()}`, {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to export leads');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = `leads-export-${format(new Date(), 'yyyy-MM-dd')}.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast({
        title: 'Success',
        description: 'Leads exported successfully.',
      });
    } catch (error) {
      console.error('Error exporting leads:', error);
      toast({
        title: 'Error',
        description: 'Failed to export leads. Please try again.',
        variant: 'destructive',
      });
    }
  };

  // Delete lead
  const handleDelete = async (leadId: string) => {
    if (!confirm('Are you sure you want to delete this lead?')) {
      return;
    }

    try {
      const response = await fetch(`/api/sales/leads/${leadId}`, {
        method: 'DELETE',
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to delete lead');
      }

      toast({
        title: 'Success',
        description: 'Lead deleted successfully.',
      });

      fetchLeads(); // Refresh the list
    } catch (error) {
      console.error('Error deleting lead:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete lead. Please try again.',
        variant: 'destructive',
      });
    }
  };

  // Bulk delete leads
  const handleBulkDelete = async () => {
    if (selectedLeads.length === 0) {
      toast({
        title: 'Error',
        description: 'Please select leads to delete.',
        variant: 'destructive',
      });
      return;
    }

    if (!confirm(`Are you sure you want to delete ${selectedLeads.length} selected leads?`)) {
      return;
    }

    try {
      setBulkLoading(true);

      const response = await fetch('/api/sales/leads/bulk', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          action: 'DELETE',
          leadIds: selectedLeads,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to delete leads');
      }

      const result = await response.json();

      toast({
        title: 'Success',
        description: result.message || 'Leads deleted successfully.',
      });

      setSelectedLeads([]);
      fetchLeads(); // Refresh the list
    } catch (error) {
      console.error('Error deleting leads:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete leads. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setBulkLoading(false);
    }
  };

  // Bulk update status
  const handleBulkStatusUpdate = async (newStatus: string) => {
    if (selectedLeads.length === 0) {
      toast({
        title: 'Error',
        description: 'Please select leads to update.',
        variant: 'destructive',
      });
      return;
    }

    try {
      setBulkLoading(true);

      const response = await fetch('/api/sales/leads/bulk', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          action: 'UPDATE_STATUS',
          leadIds: selectedLeads,
          status: newStatus,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update lead status');
      }

      const result = await response.json();

      toast({
        title: 'Success',
        description: result.message || 'Lead status updated successfully.',
      });

      setSelectedLeads([]);
      fetchLeads(); // Refresh the list
    } catch (error) {
      console.error('Error updating lead status:', error);
      toast({
        title: 'Error',
        description: 'Failed to update lead status. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setBulkLoading(false);
    }
  };

  // Bulk export selected leads
  const handleBulkExport = async () => {
    if (selectedLeads.length === 0) {
      toast({
        title: 'Error',
        description: 'Please select leads to export.',
        variant: 'destructive',
      });
      return;
    }

    try {
      setBulkLoading(true);

      const response = await fetch('/api/sales/leads/bulk', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          action: 'EXPORT',
          leadIds: selectedLeads,
          exportFormat: 'CSV',
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to export leads');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = `leads-bulk-export-${format(new Date(), 'yyyy-MM-dd')}.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast({
        title: 'Success',
        description: 'Selected leads exported successfully.',
      });
    } catch (error) {
      console.error('Error exporting leads:', error);
      toast({
        title: 'Error',
        description: 'Failed to export leads. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setBulkLoading(false);
    }
  };

  // Table columns definition
  const columns: ColumnDef<Lead>[] = [
    {
      id: 'select',
      header: ({ table }) => (
        <input
          type="checkbox"
          checked={table.getIsAllPageRowsSelected()}
          onChange={(e) => {
            table.toggleAllPageRowsSelected(e.target.checked);
            if (e.target.checked) {
              setSelectedLeads(leads.map(lead => lead.id));
            } else {
              setSelectedLeads([]);
            }
          }}
          className="rounded border-gray-300"
        />
      ),
      cell: ({ row }) => (
        <input
          type="checkbox"
          checked={selectedLeads.includes(row.original.id)}
          onChange={(e) => {
            if (e.target.checked) {
              setSelectedLeads(prev => [...prev, row.original.id]);
            } else {
              setSelectedLeads(prev => prev.filter(id => id !== row.original.id));
            }
          }}
          className="rounded border-gray-300"
        />
      ),
    },
    {
      accessorKey: 'customer.name',
      header: 'Customer',
      cell: ({ row }) => (
        <div className="flex flex-col">
          <span className="font-medium">{row.original.customer.name}</span>
          {row.original.customer.city && (
            <span className="text-sm text-gray-500">{row.original.customer.city}</span>
          )}
        </div>
      ),
    },
    {
      accessorKey: 'contactPerson',
      header: 'Contact Person',
      cell: ({ row }) => (
        <div className="flex flex-col">
          {row.original.contactPerson && (
            <span className="font-medium">{row.original.contactPerson}</span>
          )}
          {row.original.contactPhone && (
            <span className="text-sm text-gray-500">{row.original.contactPhone}</span>
          )}
        </div>
      ),
    },
    {
      accessorKey: 'executive.name',
      header: 'Executive',
      cell: ({ row }) => (
        <div className="flex flex-col">
          <span className="font-medium">{row.original.executive.name}</span>
          {row.original.executive.designation && (
            <span className="text-sm text-gray-500">{row.original.executive.designation}</span>
          )}
        </div>
      ),
    },
    {
      accessorKey: 'leadDate',
      header: 'Lead Date',
      cell: ({ row }) => format(new Date(row.original.leadDate), 'MMM dd, yyyy'),
    },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: ({ row }) => (
        <Badge className={getStatusColor(row.original.status)}>
          {row.original.status.replace('_', ' ')}
        </Badge>
      ),
    },
    {
      accessorKey: 'prospectPercentage',
      header: 'Prospect %',
      cell: ({ row }) => (
        row.original.prospectPercentage ? `${row.original.prospectPercentage}%` : '-'
      ),
    },
    {
      accessorKey: 'followUpDate',
      header: 'Follow Up',
      cell: ({ row }) => (
        row.original.followUpDate 
          ? format(new Date(row.original.followUpDate), 'MMM dd, yyyy')
          : '-'
      ),
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem asChild>
              <Link href={`/leads/${row.original.id}`}>
                <Eye className="mr-2 h-4 w-4" />
                View Details
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Link href={`/leads/${row.original.id}/edit`}>
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem 
              onClick={() => handleDelete(row.original.id)}
              className="text-red-600"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader className="bg-primary text-primary-foreground">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-white">Lead Management</CardTitle>
              <CardDescription className="text-primary-foreground/80">
                Manage and track sales leads
              </CardDescription>
            </div>
            <Button asChild variant="secondary">
              <Link href="/leads/new">
                <Plus className="mr-2 h-4 w-4" />
                New Lead
              </Link>
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-black">Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label htmlFor="search" className="text-black">Search</Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  id="search"
                  placeholder="Search customers, contacts..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="status" className="text-black">Status</Label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  {statusOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="startDate" className="text-black">Start Date</Label>
              <Input
                id="startDate"
                type="date"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="endDate" className="text-black">End Date</Label>
              <Input
                id="endDate"
                type="date"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
              />
            </div>
          </div>

          <div className="flex items-center gap-2 mt-4">
            <Button onClick={handleSearch} size="sm">
              <Search className="mr-2 h-4 w-4" />
              Search
            </Button>
            <Button onClick={clearFilters} variant="outline" size="sm">
              Clear Filters
            </Button>
            <Button onClick={handleExport} variant="outline" size="sm">
              <Download className="mr-2 h-4 w-4" />
              Export
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Bulk Operations */}
      {selectedLeads.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-black">
              Bulk Operations ({selectedLeads.length} selected)
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <Button
                onClick={handleBulkExport}
                variant="outline"
                size="sm"
                disabled={bulkLoading}
              >
                <Download className="mr-2 h-4 w-4" />
                Export Selected
              </Button>

              <Select onValueChange={handleBulkStatusUpdate} disabled={bulkLoading}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Update Status" />
                </SelectTrigger>
                <SelectContent>
                  {statusOptions.filter(option => option.value !== 'all').map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Button
                onClick={handleBulkDelete}
                variant="destructive"
                size="sm"
                disabled={bulkLoading}
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete Selected
              </Button>

              <Button
                onClick={() => setSelectedLeads([])}
                variant="outline"
                size="sm"
                disabled={bulkLoading}
              >
                Clear Selection
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Results */}
      <Card>
        <CardHeader>
          <CardTitle className="text-black">
            Leads ({totalCount} total)
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="flex items-center space-x-4">
                  <Skeleton className="h-12 w-12 rounded-full" />
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-[200px]" />
                    <Skeleton className="h-4 w-[150px]" />
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <DataTable
              columns={columns}
              data={leads}
              pagination={{
                pageIndex: currentPage - 1,
                pageSize,
                pageCount: totalPages,
                total: totalCount,
                onPageChange: (page) => setCurrentPage(page + 1),
                onPageSizeChange: setPageSize,
              }}
            />
          )}
        </CardContent>
      </Card>
    </div>
  );
}
