import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { getSalesLeadRepository } from '@/lib/repositories';
import { updateSalesLeadSchema } from '@/lib/validations/sales.schema';
import { z } from 'zod';

/**
 * GET /api/sales/leads/[id]
 * Get a specific sales lead by ID
 */
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  async (
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      const { id } = await params;

      const salesLeadRepository = getSalesLeadRepository();
      const salesLead = await salesLeadRepository.findById(id);

      if (!salesLead) {
        return NextResponse.json(
          {
            success: false,
            error: 'Sales lead not found',
          },
          { status: 404 }
        );
      }

      return NextResponse.json({
        success: true,
        data: salesLead,
      });
    } catch (error) {
      console.error('Error fetching sales lead:', error);
      
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to fetch sales lead',
        },
        { status: 500 }
      );
    }
  }
);

/**
 * PUT /api/sales/leads/[id]
 * Update a specific sales lead
 */
export const PUT = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  async (
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      const { id } = await params;
      const body = await request.json();

      // Validate request body
      const validatedData = updateSalesLeadSchema.parse({ ...body, id });

      const salesLeadRepository = getSalesLeadRepository();

      // Check if sales lead exists
      const existingSalesLead = await salesLeadRepository.findById(id);
      if (!existingSalesLead) {
        return NextResponse.json(
          {
            success: false,
            error: 'Sales lead not found',
          },
          { status: 404 }
        );
      }

      // Update sales lead
      const updateData: any = {};
      
      if (validatedData.customerId) updateData.customerId = validatedData.customerId;
      if (validatedData.executiveId) updateData.executiveId = validatedData.executiveId;
      if (validatedData.leadDate) updateData.leadDate = validatedData.leadDate;
      if (validatedData.contactPerson !== undefined) updateData.contactPerson = validatedData.contactPerson;
      if (validatedData.contactPhone !== undefined) updateData.contactPhone = validatedData.contactPhone;
      if (validatedData.status) updateData.status = validatedData.status;
      if (validatedData.prospectPercentage !== undefined) updateData.prospectPercentage = validatedData.prospectPercentage;
      if (validatedData.followUpDate !== undefined) updateData.followUpDate = validatedData.followUpDate;
      if (validatedData.nextVisitDate !== undefined) updateData.nextVisitDate = validatedData.nextVisitDate;
      if (validatedData.remarks !== undefined) updateData.remarks = validatedData.remarks;

      const updatedSalesLead = await salesLeadRepository.update(id, updateData);

      return NextResponse.json({
        success: true,
        data: updatedSalesLead,
        message: 'Sales lead updated successfully',
      });
    } catch (error) {
      console.error('Error updating sales lead:', error);
      
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid request data',
            details: error.errors,
          },
          { status: 400 }
        );
      }
      
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to update sales lead',
        },
        { status: 500 }
      );
    }
  }
);

/**
 * DELETE /api/sales/leads/[id]
 * Delete a specific sales lead
 */
export const DELETE = withRoleProtection(
  ['ADMIN', 'MANAGER'],
  async (
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      const { id } = await params;

      const salesLeadRepository = getSalesLeadRepository();

      // Check if sales lead exists
      const existingSalesLead = await salesLeadRepository.findById(id);
      if (!existingSalesLead) {
        return NextResponse.json(
          {
            success: false,
            error: 'Sales lead not found',
          },
          { status: 404 }
        );
      }

      // Delete sales lead
      await salesLeadRepository.delete(id);

      return NextResponse.json({
        success: true,
        message: 'Sales lead deleted successfully',
      });
    } catch (error) {
      console.error('Error deleting sales lead:', error);
      
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to delete sales lead',
        },
        { status: 500 }
      );
    }
  }
);
