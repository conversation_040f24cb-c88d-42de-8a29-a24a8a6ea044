'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  ArrowRightLeft, 
  Search, 
  Filter, 
  FileDown, 
  Plus, 
  Eye, 
  AlertTriangle,
  CheckCircle,
  Clock,
  Calendar,
  Shield,
  ShieldCheck,
  RefreshCw
} from 'lucide-react';
import Link from 'next/link';
import { format } from 'date-fns';
import { ConversionHistory } from '@/components/warranties/conversion-history';
import { WarrantyConversionActions } from '@/components/warranties/warranty-conversion-actions';
import { toast } from '@/components/ui/use-toast';

/**
 * Warranty Conversion Management Page
 * 
 * This page provides a comprehensive interface for managing warranty conversions,
 * including viewing eligible warranties, conversion history, and bulk operations.
 */
export default function WarrantyConversionsPage() {
  const [eligibleWarranties, setEligibleWarranties] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Load eligible warranties for conversion
  useEffect(() => {
    const loadEligibleWarranties = async () => {
      try {
        setIsLoading(true);

        const response = await fetch('/api/warranties?status=ACTIVE&eligible_for_conversion=true', {
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error('Failed to fetch eligible warranties');
        }

        const data = await response.json();
        setEligibleWarranties(data.warranties || []);
        setError(null);
      } catch (err) {
        console.error('Error loading eligible warranties:', err);
        setError('Failed to load eligible warranties');
        setEligibleWarranties([]);
      } finally {
        setIsLoading(false);
      }
    };

    loadEligibleWarranties();
  }, [refreshTrigger]);

  const handleConversionSuccess = () => {
    // Refresh the warranties list after successful conversion
    setRefreshTrigger(prev => prev + 1);
    toast({
      title: 'Conversion Successful',
      description: 'The warranty has been successfully converted.',
    });
  };

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'PPP');
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const filteredWarranties = eligibleWarranties.filter(warranty => {
    const matchesSearch = searchTerm === '' || 
      warranty.bslNo?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      warranty.customer?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      warranty.machines?.some((machine: any) => 
        machine.serialNumber?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    
    return matchesSearch;
  });

  const getWarrantyStatusBadge = (warrantyDate: string) => {
    const today = new Date();
    const warranty = new Date(warrantyDate);
    const daysUntilExpiry = Math.ceil((warranty.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));

    if (daysUntilExpiry <= 0) {
      return <Badge variant="destructive" className="flex items-center space-x-1">
        <AlertTriangle className="h-3 w-3" />
        <span>Expired</span>
      </Badge>;
    }

    if (daysUntilExpiry <= 30) {
      return <Badge variant="secondary" className="flex items-center space-x-1 bg-yellow-100 text-yellow-800">
        <Clock className="h-3 w-3" />
        <span>Expiring Soon</span>
      </Badge>;
    }

    return <Badge variant="secondary" className="flex items-center space-x-1 bg-green-100 text-green-800">
      <CheckCircle className="h-3 w-3" />
      <span>Active</span>
    </Badge>;
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="pb-3 flex flex-row items-center justify-between bg-primary text-white">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <ArrowRightLeft className="h-5 w-5" />
              <span>Warranty Conversion Management</span>
            </CardTitle>
            <CardDescription className="text-gray-100">
              Manage warranty to AMC conversions and view conversion history
            </CardDescription>
          </div>
          <div className="flex space-x-2">
            <Button 
              variant="secondary" 
              onClick={() => setRefreshTrigger(prev => prev + 1)}
              disabled={isLoading}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </CardHeader>
        <CardContent className="pt-6">
          <Tabs defaultValue="eligible" className="space-y-6">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="eligible">Eligible for Conversion</TabsTrigger>
              <TabsTrigger value="history">Conversion History</TabsTrigger>
            </TabsList>

            <TabsContent value="eligible" className="space-y-6">
              {/* Search Filter */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="search" className="text-black">Search</Label>
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      id="search"
                      placeholder="Search by BSL No, customer, or serial number..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
              </div>

              {/* Error State */}
              {error && (
                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription className="text-black">{error}</AlertDescription>
                </Alert>
              )}

              {/* Eligible Warranties Table */}
              <div className="border rounded-md">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="text-black">BSL No</TableHead>
                      <TableHead className="text-black">Customer</TableHead>
                      <TableHead className="text-black">Install Date</TableHead>
                      <TableHead className="text-black">Warranty Date</TableHead>
                      <TableHead className="text-black">Machines</TableHead>
                      <TableHead className="text-black">Amount</TableHead>
                      <TableHead className="text-black">Status</TableHead>
                      <TableHead className="text-center text-black">Convert to AMC</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {isLoading ? (
                      // Loading skeleton
                      Array.from({ length: 5 }).map((_, index) => (
                        <TableRow key={`skeleton-${index}`}>
                          <TableCell><Skeleton className="h-6 w-20" /></TableCell>
                          <TableCell><Skeleton className="h-6 w-32" /></TableCell>
                          <TableCell><Skeleton className="h-6 w-24" /></TableCell>
                          <TableCell><Skeleton className="h-6 w-24" /></TableCell>
                          <TableCell><Skeleton className="h-6 w-16" /></TableCell>
                          <TableCell><Skeleton className="h-6 w-20" /></TableCell>
                          <TableCell><Skeleton className="h-6 w-20" /></TableCell>
                          <TableCell className="text-center"><Skeleton className="h-8 w-24 mx-auto" /></TableCell>
                        </TableRow>
                      ))
                    ) : filteredWarranties.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={8} className="text-center py-8">
                          <div className="flex flex-col items-center space-y-2">
                            <ArrowRightLeft className="h-8 w-8 text-gray-400" />
                            <p className="text-gray-500">No warranties eligible for conversion found</p>
                            <Button asChild>
                              <Link href="/warranties/in-warranty">
                                <Shield className="h-4 w-4 mr-2" />
                                View All Warranties
                              </Link>
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredWarranties.map((warranty) => (
                        <TableRow key={warranty.id}>
                          <TableCell className="font-medium text-black">{warranty.bslNo}</TableCell>
                          <TableCell className="text-black">
                            <div>
                              <div className="font-medium">{warranty.customer?.name}</div>
                              <div className="text-sm text-gray-500">{warranty.customer?.city}</div>
                            </div>
                          </TableCell>
                          <TableCell className="text-black">{formatDate(warranty.installDate)}</TableCell>
                          <TableCell className="text-black">{formatDate(warranty.warrantyDate)}</TableCell>
                          <TableCell className="text-black">{warranty.numberOfMachines}</TableCell>
                          <TableCell className="text-black">{formatCurrency(warranty.bslAmount || 0)}</TableCell>
                          <TableCell>{getWarrantyStatusBadge(warranty.warrantyDate)}</TableCell>
                          <TableCell className="text-center">
                            <WarrantyConversionActions
                              warranty={{
                                id: warranty.id,
                                customerId: warranty.customerId,
                                customerName: warranty.customer?.name,
                                bslNo: warranty.bslNo,
                                numberOfMachines: warranty.numberOfMachines,
                                status: warranty.status,
                                executiveId: warranty.executiveId,
                                contactPersonId: warranty.contactPersonId,
                              }}
                              onConversionSuccess={handleConversionSuccess}
                              variant="button"
                              size="sm"
                            />
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>

              {/* Summary */}
              {!isLoading && filteredWarranties.length > 0 && (
                <div className="flex items-center justify-between mt-4">
                  <p className="text-sm text-gray-600">
                    Showing {filteredWarranties.length} of {eligibleWarranties.length} eligible warranties
                  </p>
                </div>
              )}
            </TabsContent>

            <TabsContent value="history" className="space-y-6">
              <ConversionHistory showFilters={true} maxItems={20} />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
