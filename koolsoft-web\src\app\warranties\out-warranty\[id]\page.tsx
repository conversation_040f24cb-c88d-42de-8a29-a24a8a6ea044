'use client';

import { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import Link from 'next/link';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';
import {
  XCircle,
  Edit,
  ArrowLeft,
  User,
  Phone,
  Calendar,
  DollarSign,
  AlertTriangle,
  Settings,
  FileText,
  Wrench
} from 'lucide-react';

interface OutWarrantyDetails {
  id: string;
  customerId: string;
  executiveId: string;
  contactPerson?: string;
  contactPhone?: string;
  startDate: string;
  endDate: string;
  amount: number;
  source: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  customer: {
    id: string;
    name: string;
    city: string;
    phone?: string;
    address?: string;
  };
  executive: {
    id: string;
    name: string;
    email: string;
    phone?: string;
  };
  machines?: Array<{
    id: string;
    serialNumber: string;
    model: {
      name: string;
      product: {
        name: string;
        brand: {
          name: string;
        };
      };
    };
    components?: Array<{
      id: string;
      serialNumber: string;
      componentNo: number;
    }>;
  }>;
  payments?: Array<{
    id: string;
    receiptNo?: string;
    paymentDate: string;
    amount: number;
    paymentMode?: string;
    particulars?: string;
  }>;
}

export default function OutWarrantyDetailPage() {
  const params = useParams();
  const router = useRouter();
  const outWarrantyId = params?.id as string;
  
  const [outWarranty, setOutWarranty] = useState<OutWarrantyDetails | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadOutWarranty = async () => {
      try {
        setIsLoading(true);
        
        const response = await fetch(`/api/out-warranties/${outWarrantyId}`, {
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          if (response.status === 404) {
            throw new Error('Out-warranty record not found');
          }
          throw new Error('Failed to fetch out-warranty details');
        }

        const data = await response.json();
        setOutWarranty(data);
        setError(null);
      } catch (err: any) {
        console.error('Error loading out-warranty:', err);
        setError(err.message || 'Failed to load out-warranty details');
      } finally {
        setIsLoading(false);
      }
    };

    if (outWarrantyId) {
      loadOutWarranty();
    }
  }, [outWarrantyId]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN');
  };

  const totalPayments = outWarranty?.payments?.reduce((sum, payment) => sum + payment.amount, 0) || 0;
  const balance = (outWarranty?.amount || 0) - totalPayments;

  if (isLoading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader className="pb-3 bg-primary text-white">
            <Skeleton className="h-6 w-48 bg-white/20" />
            <Skeleton className="h-4 w-64 bg-white/20" />
          </CardHeader>
          <CardContent className="pt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Skeleton className="h-32" />
              <Skeleton className="h-32" />
              <Skeleton className="h-32" />
              <Skeleton className="h-32" />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription className="text-black">{error}</AlertDescription>
      </Alert>
    );
  }

  if (!outWarranty) {
    return (
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription className="text-black">Out-warranty record not found</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="pb-3 flex flex-row items-center justify-between bg-primary text-white">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <XCircle className="h-5 w-5" />
              <span>Out-Warranty Details</span>
            </CardTitle>
            <CardDescription className="text-gray-100">
              View out-warranty information and service history
            </CardDescription>
          </div>
          <div className="flex space-x-2">
            <Button variant="secondary" asChild>
              <Link href="/warranties/out-warranty">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to List
              </Link>
            </Button>
            <Button variant="secondary" asChild>
              <Link href={`/warranties/out-warranty/${outWarrantyId}/edit`}>
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Link>
            </Button>
          </div>
        </CardHeader>
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Customer Information */}
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="flex items-center space-x-2 text-black">
                    <User className="h-4 w-4" />
                    <span>Customer Information</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div>
                    <label className="text-sm font-medium text-gray-600">Name</label>
                    <p className="text-black font-medium">{outWarranty.customer.name}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">City</label>
                    <p className="text-black">{outWarranty.customer.city}</p>
                  </div>
                  {outWarranty.customer.phone && (
                    <div>
                      <label className="text-sm font-medium text-gray-600">Phone</label>
                      <p className="text-black">{outWarranty.customer.phone}</p>
                    </div>
                  )}
                  {outWarranty.customer.address && (
                    <div>
                      <label className="text-sm font-medium text-gray-600">Address</label>
                      <p className="text-black">{outWarranty.customer.address}</p>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Out-Warranty Information */}
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="flex items-center space-x-2 text-black">
                    <FileText className="h-4 w-4" />
                    <span>Out-Warranty Information</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div>
                    <label className="text-sm font-medium text-gray-600">Start Date</label>
                    <p className="text-black">{formatDate(outWarranty.startDate)}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">End Date</label>
                    <p className="text-black">{formatDate(outWarranty.endDate)}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Amount</label>
                    <p className="text-black font-medium">{formatCurrency(outWarranty.amount)}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Source</label>
                    <Badge variant="secondary">{outWarranty.source}</Badge>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Status</label>
                    <Badge variant={outWarranty.isActive ? "default" : "secondary"}>
                      {outWarranty.isActive ? 'Active' : 'Inactive'}
                    </Badge>
                  </div>
                </CardContent>
              </Card>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
