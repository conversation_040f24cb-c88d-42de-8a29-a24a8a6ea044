import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { 
  getSalesLeadRepository, 
  getSalesOpportunityRepository, 
  getSalesProspectRepository, 
  getSalesOrderRepository 
} from '@/lib/repositories';
import { z } from 'zod';

/**
 * Statistics filter schema
 */
const statisticsFilterSchema = z.object({
  customerId: z.string().uuid().optional(),
  executiveId: z.string().uuid().optional(),
  startDate: z.coerce.date().optional(),
  endDate: z.coerce.date().optional(),
}).refine(
  (data) => {
    // End date should be after start date if both provided
    if (data.startDate && data.endDate) {
      return data.endDate >= data.startDate;
    }
    return true;
  },
  {
    message: 'End date must be after or equal to start date',
    path: ['endDate'],
  }
);

/**
 * GET /api/sales/statistics
 * Get comprehensive sales statistics across all sales entities
 */
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  async (request: NextRequest) => {
    try {
      const { searchParams } = new URL(request.url);
      
      // Parse and validate query parameters
      const filters = {
        customerId: searchParams.get('customerId') || undefined,
        executiveId: searchParams.get('executiveId') || undefined,
        startDate: searchParams.get('startDate') || undefined,
        endDate: searchParams.get('endDate') || undefined,
      };

      const validatedFilters = statisticsFilterSchema.parse(filters);

      // Get repository instances
      const salesLeadRepository = getSalesLeadRepository();
      const salesOpportunityRepository = getSalesOpportunityRepository();
      const salesProspectRepository = getSalesProspectRepository();
      const salesOrderRepository = getSalesOrderRepository();

      // Fetch statistics from all repositories in parallel
      const [
        leadStatistics,
        opportunityStatistics,
        prospectStatistics,
        orderStatistics,
      ] = await Promise.all([
        salesLeadRepository.getStatistics(validatedFilters),
        salesOpportunityRepository.getStatistics(validatedFilters),
        salesProspectRepository.getStatistics(validatedFilters),
        salesOrderRepository.getStatistics(validatedFilters),
      ]);

      // Calculate overall sales funnel metrics
      const totalLeads = leadStatistics.total;
      const totalOpportunities = opportunityStatistics.total;
      const totalProspects = prospectStatistics.total;
      const totalOrders = orderStatistics.total;

      // Calculate conversion rates
      const leadToOpportunityRate = totalLeads > 0 ? (totalOpportunities / totalLeads) * 100 : 0;
      const opportunityToProspectRate = totalOpportunities > 0 ? (totalProspects / totalOpportunities) * 100 : 0;
      const prospectToOrderRate = totalProspects > 0 ? (totalOrders / totalProspects) * 100 : 0;
      const leadToOrderRate = totalLeads > 0 ? (totalOrders / totalLeads) * 100 : 0;

      // Aggregate statistics
      const aggregatedStatistics = {
        overview: {
          totalLeads,
          totalOpportunities,
          totalProspects,
          totalOrders,
          totalRevenue: orderStatistics.totalAmount,
          averageOrderValue: orderStatistics.averageOrderValue,
        },
        conversionRates: {
          leadToOpportunity: leadToOpportunityRate,
          opportunityToProspect: opportunityToProspectRate,
          prospectToOrder: prospectToOrderRate,
          leadToOrder: leadToOrderRate,
        },
        leads: {
          total: leadStatistics.total,
          new: leadStatistics.newLeads,
          qualified: leadStatistics.qualifiedLeads,
          closedWon: leadStatistics.closedWonLeads,
          closedLost: leadStatistics.closedLostLeads,
          averageProspectPercentage: leadStatistics.averageProspectPercentage,
          conversionRate: leadStatistics.conversionRate,
        },
        opportunities: {
          total: opportunityStatistics.total,
          open: opportunityStatistics.openOpportunities,
          qualified: opportunityStatistics.qualifiedOpportunities,
          closedWon: opportunityStatistics.closedWonOpportunities,
          closedLost: opportunityStatistics.closedLostOpportunities,
          averageProspectPercentage: opportunityStatistics.averageProspectPercentage,
          conversionRate: opportunityStatistics.conversionRate,
        },
        prospects: {
          total: prospectStatistics.total,
          active: prospectStatistics.activeProspects,
          qualified: prospectStatistics.qualifiedProspects,
          converted: prospectStatistics.convertedProspects,
          lost: prospectStatistics.lostProspects,
          averageProspectPercentage: prospectStatistics.averageProspectPercentage,
          conversionRate: prospectStatistics.conversionRate,
        },
        orders: {
          total: orderStatistics.total,
          pending: orderStatistics.pendingOrders,
          confirmed: orderStatistics.confirmedOrders,
          completed: orderStatistics.completedOrders,
          cancelled: orderStatistics.cancelledOrders,
          totalAmount: orderStatistics.totalAmount,
          averageOrderValue: orderStatistics.averageOrderValue,
        },
      };

      return NextResponse.json({
        success: true,
        data: aggregatedStatistics,
        filters: validatedFilters,
      });
    } catch (error) {
      console.error('Error fetching sales statistics:', error);
      
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid request parameters',
            details: error.errors,
          },
          { status: 400 }
        );
      }
      
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to fetch sales statistics',
        },
        { status: 500 }
      );
    }
  }
);
