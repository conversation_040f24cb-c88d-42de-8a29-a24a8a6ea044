import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { getSalesLeadRepository } from '@/lib/repositories';
import { z } from 'zod';
import { format } from 'date-fns';

// Bulk operation validation schema
const bulkOperationSchema = z.object({
  action: z.enum(['DELETE', 'UPDATE_STATUS', 'EXPORT']),
  leadIds: z.array(z.string().uuid()).min(1, 'At least one lead ID is required'),
  // For UPDATE_STATUS action
  status: z.string().optional(),
  // For EXPORT action
  exportFormat: z.enum(['CSV', 'EXCEL']).optional(),
});

/**
 * Generate CSV export for bulk selected leads
 */
function generateCSVExport(leads: any[]): NextResponse {
  // CSV headers
  const headers = [
    'Lead ID',
    'Customer Name',
    'Customer City',
    'Customer Phone',
    'Customer Email',
    'Executive Name',
    'Executive Designation',
    'Lead Date',
    'Contact Person',
    'Contact Phone',
    'Status',
    'Prospect Percentage',
    'Follow Up Date',
    'Next Visit Date',
    'Remarks',
    'Created At',
    'Updated At'
  ];

  // Convert leads to CSV rows
  const csvRows = leads.map(lead => [
    lead.id,
    lead.customer?.name || '',
    lead.customer?.city || '',
    lead.customer?.phone || '',
    lead.customer?.email || '',
    lead.executive?.name || '',
    lead.executive?.designation || '',
    format(new Date(lead.leadDate), 'yyyy-MM-dd'),
    lead.contactPerson || '',
    lead.contactPhone || '',
    lead.status,
    lead.prospectPercentage || '',
    lead.followUpDate ? format(new Date(lead.followUpDate), 'yyyy-MM-dd') : '',
    lead.nextVisitDate ? format(new Date(lead.nextVisitDate), 'yyyy-MM-dd') : '',
    lead.remarks || '',
    format(new Date(lead.createdAt), 'yyyy-MM-dd HH:mm:ss'),
    format(new Date(lead.updatedAt), 'yyyy-MM-dd HH:mm:ss')
  ]);

  // Escape CSV values and join
  const escapeCsvValue = (value: any) => {
    const str = String(value || '');
    if (str.includes(',') || str.includes('"') || str.includes('\n')) {
      return `"${str.replace(/"/g, '""')}"`;
    }
    return str;
  };

  const csvHeader = headers.map(escapeCsvValue).join(',');
  const csvData = csvRows.map(row => row.map(escapeCsvValue).join(',')).join('\n');
  const csv = `${csvHeader}\n${csvData}`;

  // Generate filename
  const now = format(new Date(), 'yyyy-MM-dd_HHmmss');
  const filename = `leads_bulk_export_${now}.csv`;

  return new NextResponse(csv, {
    headers: {
      'Content-Type': 'text/csv',
      'Content-Disposition': `attachment; filename="${filename}"`,
    },
  });
}

/**
 * POST /api/sales/leads/bulk
 * Perform bulk operations on leads
 */
export const POST = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  async (request: NextRequest) => {
    try {
      const body = await request.json();
      
      // Validate request body
      const validatedData = bulkOperationSchema.parse(body);
      const { action, leadIds, status, exportFormat } = validatedData;

      const salesLeadRepository = getSalesLeadRepository();

      switch (action) {
        case 'DELETE':
          // Verify all leads exist before deletion
          const leadsToDelete = await Promise.all(
            leadIds.map(id => salesLeadRepository.findById(id))
          );
          
          const missingLeads = leadIds.filter((id, index) => !leadsToDelete[index]);
          if (missingLeads.length > 0) {
            return NextResponse.json(
              {
                success: false,
                error: `Leads not found: ${missingLeads.join(', ')}`,
              },
              { status: 404 }
            );
          }

          // Delete all leads
          await Promise.all(leadIds.map(id => salesLeadRepository.delete(id)));

          return NextResponse.json({
            success: true,
            message: `Successfully deleted ${leadIds.length} leads`,
            deletedCount: leadIds.length,
          });

        case 'UPDATE_STATUS':
          if (!status) {
            return NextResponse.json(
              { error: 'Status is required for UPDATE_STATUS action' },
              { status: 400 }
            );
          }

          // Verify all leads exist before updating
          const leadsToUpdate = await Promise.all(
            leadIds.map(id => salesLeadRepository.findById(id))
          );
          
          const missingLeadsForUpdate = leadIds.filter((id, index) => !leadsToUpdate[index]);
          if (missingLeadsForUpdate.length > 0) {
            return NextResponse.json(
              {
                success: false,
                error: `Leads not found: ${missingLeadsForUpdate.join(', ')}`,
              },
              { status: 404 }
            );
          }

          // Update status for all leads
          const updatePromises = leadIds.map(id => 
            salesLeadRepository.updateStatus(id, status)
          );
          await Promise.all(updatePromises);

          return NextResponse.json({
            success: true,
            message: `Successfully updated status to "${status}" for ${leadIds.length} leads`,
            updatedCount: leadIds.length,
          });

        case 'EXPORT':
          if (!exportFormat) {
            return NextResponse.json(
              { error: 'Export format is required for EXPORT action' },
              { status: 400 }
            );
          }

          // Get leads for export
          const leadsForExport = await Promise.all(
            leadIds.map(id => salesLeadRepository.findById(id))
          );
          
          const validLeads = leadsForExport.filter(lead => lead !== null);
          
          if (validLeads.length === 0) {
            return NextResponse.json(
              { error: 'No valid leads found for export' },
              { status: 404 }
            );
          }

          // Generate export based on format
          switch (exportFormat) {
            case 'CSV':
              return generateCSVExport(validLeads);
            case 'EXCEL':
              // For now, return CSV format for Excel as well
              return generateCSVExport(validLeads);
            default:
              return NextResponse.json(
                { error: 'Unsupported export format' },
                { status: 400 }
              );
          }

        default:
          return NextResponse.json(
            { error: 'Unsupported bulk action' },
            { status: 400 }
          );
      }
    } catch (error) {
      console.error('Error performing bulk operation:', error);
      
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid request parameters',
            details: error.errors,
          },
          { status: 400 }
        );
      }
      
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to perform bulk operation',
        },
        { status: 500 }
      );
    }
  }
);
