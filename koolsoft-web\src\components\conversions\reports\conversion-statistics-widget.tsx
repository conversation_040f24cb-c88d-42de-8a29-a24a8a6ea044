'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  ArrowRightLeft, 
  TrendingUp, 
  TrendingDown, 
  DollarSign,
  BarChart3,
  AlertCircle 
} from 'lucide-react';
import { ConversionStatistics } from '@/lib/validations/conversion.schema';
import { toast } from 'sonner';

interface ConversionStatisticsWidgetProps {
  className?: string;
  showTitle?: boolean;
  compact?: boolean;
}

/**
 * Conversion Statistics Widget Component
 * 
 * Displays key conversion metrics for dashboard integration
 */
export function ConversionStatisticsWidget({ 
  className = '', 
  showTitle = true,
  compact = false 
}: ConversionStatisticsWidgetProps) {
  const [statistics, setStatistics] = useState<ConversionStatistics | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchStatistics();
  }, []);

  const fetchStatistics = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch('/api/conversions/reports/statistics', {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to fetch conversion statistics');
      }

      const data = await response.json();
      setStatistics(data);
    } catch (error) {
      console.error('Error fetching conversion statistics:', error);
      setError('Failed to load conversion statistics');
      toast.error('Failed to load conversion statistics');
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <Card className={className}>
        {showTitle && (
          <CardHeader className="bg-primary text-white">
            <CardTitle className="flex items-center gap-2">
              <ArrowRightLeft className="h-5 w-5" />
              Conversion Statistics
            </CardTitle>
          </CardHeader>
        )}
        <CardContent className="p-6">
          <div className="space-y-4">
            <Skeleton className="h-8 w-full" />
            <Skeleton className="h-6 w-3/4" />
            <Skeleton className="h-6 w-1/2" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error || !statistics) {
    return (
      <Card className={className}>
        {showTitle && (
          <CardHeader className="bg-primary text-white">
            <CardTitle className="flex items-center gap-2">
              <ArrowRightLeft className="h-5 w-5" />
              Conversion Statistics
            </CardTitle>
          </CardHeader>
        )}
        <CardContent className="p-6">
          <div className="flex items-center gap-2 text-destructive">
            <AlertCircle className="h-5 w-5" />
            <span>{error || 'Failed to load statistics'}</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(1)}%`;
  };

  const getGrowthIcon = (growth: number) => {
    if (growth > 0) return <TrendingUp className="h-4 w-4 text-green-600" />;
    if (growth < 0) return <TrendingDown className="h-4 w-4 text-red-600" />;
    return <BarChart3 className="h-4 w-4 text-gray-600" />;
  };

  const getGrowthColor = (growth: number) => {
    if (growth > 0) return 'text-green-600';
    if (growth < 0) return 'text-red-600';
    return 'text-gray-600';
  };

  if (compact) {
    return (
      <div className={`grid grid-cols-2 gap-4 ${className}`}>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Conversions</p>
                <p className="text-2xl font-bold">{statistics.totalConversions}</p>
              </div>
              <ArrowRightLeft className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Revenue</p>
                <p className="text-2xl font-bold">{formatCurrency(statistics.totalRevenue)}</p>
              </div>
              <DollarSign className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <Card className={className}>
      {showTitle && (
        <CardHeader className="bg-primary text-white">
          <CardTitle className="flex items-center gap-2">
            <ArrowRightLeft className="h-5 w-5" />
            Conversion Statistics
          </CardTitle>
        </CardHeader>
      )}
      <CardContent className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Total Conversions */}
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <ArrowRightLeft className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium text-muted-foreground">Total Conversions</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-2xl font-bold">{statistics.totalConversions}</span>
              {statistics.periodComparison && (
                <div className={`flex items-center gap-1 ${getGrowthColor(statistics.periodComparison.growthPercentage)}`}>
                  {getGrowthIcon(statistics.periodComparison.growthPercentage)}
                  <span className="text-sm font-medium">
                    {formatPercentage(statistics.periodComparison.growthPercentage)}
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Total Revenue */}
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <DollarSign className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium text-muted-foreground">Total Revenue</span>
            </div>
            <div className="text-2xl font-bold">{formatCurrency(statistics.totalRevenue)}</div>
          </div>

          {/* Average Value */}
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium text-muted-foreground">Avg. Value</span>
            </div>
            <div className="text-2xl font-bold">{formatCurrency(statistics.averageConversionValue)}</div>
          </div>

          {/* Success Rate */}
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium text-muted-foreground">Success Rate</span>
            </div>
            <div className="text-2xl font-bold text-green-600">{statistics.successRate.toFixed(1)}%</div>
          </div>
        </div>

        {/* Conversion Types Breakdown */}
        <div className="mt-6 space-y-3">
          <h4 className="text-sm font-medium text-muted-foreground">Conversion Types</h4>
          <div className="space-y-2">
            {statistics.conversionsByType.map((type) => {
              const formatTypeName = (typeName: string) => {
                switch (typeName) {
                  case 'WARRANTY_TO_AMC':
                    return 'Warranty → AMC';
                  case 'AMC_TO_OUT_WARRANTY':
                    return 'AMC → Out-of-Warranty';
                  case 'WARRANTY_TO_OUT_WARRANTY':
                    return 'Warranty → Out-of-Warranty';
                  default:
                    return typeName;
                }
              };

              return (
                <div key={type.type} className="flex items-center justify-between">
                  <span className="text-sm font-medium">
                    {formatTypeName(type.type)}
                  </span>
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary">{type.count}</Badge>
                    <span className="text-sm text-muted-foreground">
                      {type.percentage.toFixed(1)}%
                    </span>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
