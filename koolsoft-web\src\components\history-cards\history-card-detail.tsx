'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/components/ui/use-toast';
import { 
  ArrowLeft, 
  Edit, 
  Trash2, 
  Download,
  Calendar,
  User,
  FileText,
  Hash,
  Link,
  MapPin,
  Phone,
  Building,
} from 'lucide-react';
import { format } from 'date-fns';

interface HistoryCard {
  id: string;
  cardNo?: number;
  source?: 'AMC' | 'INW' | 'OTW';
  customerId: string;
  amcId?: string;
  inWarrantyId?: string;
  outWarrantyId?: string;
  toCardNo?: number;
  originalId?: number;
  createdAt: string;
  updatedAt: string;
  customer?: {
    id: string;
    name: string;
    address?: string;
    city?: string;
    state?: string;
    phone?: string;
    email?: string;
  };
  sections?: {
    id: string;
    sectionCode: string;
    content: string;
    createdAt: string;
    updatedAt: string;
  }[];
}

interface HistoryCardDetailProps {
  historyCardId: string;
  onEdit?: (historyCard: HistoryCard) => void;
  onDelete?: (historyCardId: string) => void;
  showActions?: boolean;
}

export function HistoryCardDetail({
  historyCardId,
  onEdit,
  onDelete,
  showActions = true,
}: HistoryCardDetailProps) {
  const router = useRouter();
  const { toast } = useToast();
  
  const [historyCard, setHistoryCard] = useState<HistoryCard | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch history card details
  useEffect(() => {
    const fetchHistoryCard = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        const response = await fetch(`/api/history-cards/${historyCardId}`, {
          credentials: 'include',
        });
        
        if (!response.ok) {
          if (response.status === 404) {
            throw new Error('History card not found');
          }
          throw new Error('Failed to fetch history card');
        }
        
        const data = await response.json();
        
        if (data.success) {
          setHistoryCard(data.data);
        } else {
          throw new Error(data.error || 'Failed to fetch history card');
        }
      } catch (error) {
        console.error('Error fetching history card:', error);
        setError(error instanceof Error ? error.message : 'Failed to fetch history card');
        toast({
          title: 'Error',
          description: 'Failed to load history card details. Please try again.',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    if (historyCardId) {
      fetchHistoryCard();
    }
  }, [historyCardId, toast]);

  const handleEdit = () => {
    if (historyCard) {
      if (onEdit) {
        onEdit(historyCard);
      } else {
        router.push(`/history-cards/${historyCard.id}/edit`);
      }
    }
  };

  const handleDelete = async () => {
    if (!historyCard) return;
    
    if (onDelete) {
      onDelete(historyCard.id);
    } else {
      // Default delete implementation
      try {
        const response = await fetch(`/api/history-cards/${historyCard.id}`, {
          method: 'DELETE',
          credentials: 'include',
        });
        
        if (!response.ok) {
          throw new Error('Failed to delete history card');
        }
        
        toast({
          title: 'Success',
          description: 'History card deleted successfully.',
        });
        
        router.push('/history-cards');
      } catch (error) {
        console.error('Error deleting history card:', error);
        toast({
          title: 'Error',
          description: 'Failed to delete history card. Please try again.',
          variant: 'destructive',
        });
      }
    }
  };

  const handleExport = async () => {
    if (!historyCard) return;
    
    try {
      const response = await fetch(`/api/history-cards/export?format=csv&customerId=${historyCard.customerId}&includeDetails=true`, {
        credentials: 'include',
      });
      
      if (!response.ok) {
        throw new Error('Failed to export history card');
      }
      
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = `history-card-${historyCard.cardNo || historyCard.id}.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      
      toast({
        title: 'Success',
        description: 'History card exported successfully.',
      });
    } catch (error) {
      console.error('Error exporting history card:', error);
      toast({
        title: 'Error',
        description: 'Failed to export history card. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const getSourceBadgeVariant = (source?: string) => {
    switch (source) {
      case 'AMC':
        return 'default';
      case 'INW':
        return 'secondary';
      case 'OTW':
        return 'outline';
      default:
        return 'outline';
    }
  };

  const getSourceLabel = (source?: string) => {
    switch (source) {
      case 'AMC':
        return 'AMC';
      case 'INW':
        return 'In-Warranty';
      case 'OTW':
        return 'Out-of-Warranty';
      default:
        return 'Unknown';
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <Skeleton className="h-8 w-64" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-32" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[...Array(3)].map((_, i) => (
                <Skeleton key={i} className="h-20 w-full" />
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error || !historyCard) {
    return (
      <Alert variant="destructive">
        <AlertDescription>
          {error || 'History card not found'}
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader className="pb-3 flex flex-row items-center justify-between bg-primary text-white">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <FileText className="h-5 w-5" />
              <span>History Card #{historyCard.cardNo || 'N/A'}</span>
            </CardTitle>
            <div className="flex items-center space-x-4 mt-2">
              <Badge variant={getSourceBadgeVariant(historyCard.source)} className="bg-white text-black">
                {getSourceLabel(historyCard.source)}
              </Badge>
              <span className="text-gray-100 text-sm">
                Created {format(new Date(historyCard.createdAt), 'MMM dd, yyyy')}
              </span>
            </div>
          </div>
          {showActions && (
            <div className="flex space-x-2">
              <Button variant="secondary" size="sm" onClick={handleExport}>
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
              <Button variant="secondary" size="sm" onClick={handleEdit}>
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Button>
              <Button variant="destructive" size="sm" onClick={handleDelete}>
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </Button>
            </div>
          )}
        </CardHeader>
      </Card>

      {/* Customer Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <User className="h-5 w-5" />
            <span>Customer Information</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-500">Customer Name</label>
                <p className="text-lg font-medium">{historyCard.customer?.name || 'Unknown'}</p>
              </div>
              
              {historyCard.customer?.address && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Address</label>
                  <p className="flex items-start space-x-2">
                    <MapPin className="h-4 w-4 mt-1 text-gray-400" />
                    <span>{historyCard.customer.address}</span>
                  </p>
                </div>
              )}
              
              {historyCard.customer?.city && (
                <div>
                  <label className="text-sm font-medium text-gray-500">City</label>
                  <p className="flex items-center space-x-2">
                    <Building className="h-4 w-4 text-gray-400" />
                    <span>{historyCard.customer.city}</span>
                  </p>
                </div>
              )}
            </div>
            
            <div className="space-y-4">
              {historyCard.customer?.phone && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Phone</label>
                  <p className="flex items-center space-x-2">
                    <Phone className="h-4 w-4 text-gray-400" />
                    <span>{historyCard.customer.phone}</span>
                  </p>
                </div>
              )}
              
              <div>
                <label className="text-sm font-medium text-gray-500">Customer ID</label>
                <p className="font-mono text-sm">{historyCard.customerId}</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Card Details */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Hash className="h-5 w-5" />
            <span>Card Details</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <label className="text-sm font-medium text-gray-500">Card Number</label>
              <p className="text-lg font-medium">{historyCard.cardNo || 'N/A'}</p>
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-500">Source</label>
              <div>
                <Badge variant={getSourceBadgeVariant(historyCard.source)}>
                  {getSourceLabel(historyCard.source)}
                </Badge>
              </div>
            </div>
            
            {historyCard.toCardNo && (
              <div>
                <label className="text-sm font-medium text-gray-500">To Card Number</label>
                <p className="text-lg font-medium">{historyCard.toCardNo}</p>
              </div>
            )}
            
            {historyCard.originalId && (
              <div>
                <label className="text-sm font-medium text-gray-500">Original ID</label>
                <p className="font-mono text-sm">{historyCard.originalId}</p>
              </div>
            )}
            
            <div>
              <label className="text-sm font-medium text-gray-500">Created</label>
              <p className="flex items-center space-x-2">
                <Calendar className="h-4 w-4 text-gray-400" />
                <span>{format(new Date(historyCard.createdAt), 'MMM dd, yyyy HH:mm')}</span>
              </p>
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-500">Last Updated</label>
              <p className="flex items-center space-x-2">
                <Calendar className="h-4 w-4 text-gray-400" />
                <span>{format(new Date(historyCard.updatedAt), 'MMM dd, yyyy HH:mm')}</span>
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Reference IDs */}
      {(historyCard.amcId || historyCard.inWarrantyId || historyCard.outWarrantyId) && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Link className="h-5 w-5" />
              <span>Reference IDs</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {historyCard.amcId && (
                <div>
                  <label className="text-sm font-medium text-gray-500">AMC ID</label>
                  <p className="font-mono text-sm">{historyCard.amcId}</p>
                </div>
              )}
              
              {historyCard.inWarrantyId && (
                <div>
                  <label className="text-sm font-medium text-gray-500">In-Warranty ID</label>
                  <p className="font-mono text-sm">{historyCard.inWarrantyId}</p>
                </div>
              )}
              
              {historyCard.outWarrantyId && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Out-Warranty ID</label>
                  <p className="font-mono text-sm">{historyCard.outWarrantyId}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* History Sections */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FileText className="h-5 w-5" />
            <span>History Sections ({historyCard.sections?.length || 0})</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {historyCard.sections && historyCard.sections.length > 0 ? (
            <div className="space-y-4">
              {historyCard.sections
                .sort((a, b) => a.sectionCode.localeCompare(b.sectionCode))
                .map((section) => (
                  <Card key={section.id} className="border-l-4 border-l-blue-500">
                    <CardHeader className="pb-2">
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-lg">Section {section.sectionCode}</CardTitle>
                        <span className="text-sm text-gray-500">
                          {format(new Date(section.createdAt), 'MMM dd, yyyy')}
                        </span>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="whitespace-pre-wrap text-sm leading-relaxed">
                        {section.content}
                      </div>
                    </CardContent>
                  </Card>
                ))}
            </div>
          ) : (
            <Alert>
              <AlertDescription>
                No history sections found for this card.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
