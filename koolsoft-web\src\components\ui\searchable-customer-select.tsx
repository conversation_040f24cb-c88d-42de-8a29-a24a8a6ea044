'use client';

import { useState, useEffect, useRef } from 'react';
import { Check, ChevronsUpDown, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';

interface Customer {
  id: string;
  name: string;
  address?: string;
  city?: string;
  phone?: string;
}

interface SearchableCustomerSelectProps {
  value?: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
}

export function SearchableCustomerSelect({
  value,
  onValueChange,
  placeholder = "Select a customer...",
  disabled = false,
  className,
}: SearchableCustomerSelectProps) {
  const [open, setOpen] = useState(false);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Find selected customer when value changes
  useEffect(() => {
    if (value && customers.length > 0) {
      const customer = customers.find(c => c.id === value);
      if (customer) {
        setSelectedCustomer(customer);
      } else if (value !== selectedCustomer?.id) {
        // If value is set but customer not found in current list, fetch it
        fetchCustomerById(value);
      }
    } else if (!value) {
      setSelectedCustomer(null);
    }
  }, [value, customers]);

  // Fetch customer by ID for edit mode
  const fetchCustomerById = async (customerId: string) => {
    try {
      const response = await fetch(`/api/customers/${customerId}`, {
        credentials: 'include',
      });
      
      if (response.ok) {
        const customer = await response.json();
        setSelectedCustomer(customer);
      }
    } catch (error) {
      console.error('Error fetching customer by ID:', error);
    }
  };

  // Debounced search function
  const searchCustomers = async (query: string) => {
    try {
      setIsLoading(true);
      
      const params = new URLSearchParams({
        searchAll: query,
        take: '50', // Limit results for performance
        skip: '0',
      });

      const response = await fetch(`/api/customers/search?${params.toString()}`, {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to search customers');
      }

      const data = await response.json();
      
      if (data.success) {
        setCustomers(data.data);
      } else {
        console.error('Customer search failed:', data.error);
        setCustomers([]);
      }
    } catch (error) {
      console.error('Error searching customers:', error);
      setCustomers([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle search input change with debouncing
  const handleSearchChange = (query: string) => {
    setSearchQuery(query);
    
    // Clear existing timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // Set new timeout for debounced search
    searchTimeoutRef.current = setTimeout(() => {
      if (query.trim()) {
        searchCustomers(query.trim());
      } else {
        // Load initial customers when query is empty
        searchCustomers('');
      }
    }, 300); // 300ms debounce
  };

  // Load initial customers when component mounts or dropdown opens
  useEffect(() => {
    if (open && customers.length === 0 && !searchQuery) {
      searchCustomers('');
    }
  }, [open]);

  // Handle customer selection
  const handleCustomerSelect = (customer: Customer) => {
    setSelectedCustomer(customer);
    onValueChange(customer.id);
    setOpen(false);
  };

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            "w-full justify-between",
            !selectedCustomer && "text-muted-foreground",
            className
          )}
          disabled={disabled}
        >
          {selectedCustomer ? (
            <span className="truncate">
              {selectedCustomer.name}
              {selectedCustomer.city && ` (${selectedCustomer.city})`}
            </span>
          ) : (
            placeholder
          )}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[--radix-popover-trigger-width] p-0" align="start">
        <Command>
          <CommandInput
            placeholder="Search customers..."
            value={searchQuery}
            onValueChange={handleSearchChange}
          />
          <CommandEmpty>
            {isLoading ? (
              <div className="flex items-center justify-center py-6">
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                Searching customers...
              </div>
            ) : (
              'No customers found.'
            )}
          </CommandEmpty>
          <CommandGroup className="max-h-64 overflow-auto">
            {customers.map((customer) => (
              <CommandItem
                key={customer.id}
                value={customer.id}
                onSelect={() => handleCustomerSelect(customer)}
                className="cursor-pointer"
              >
                <Check
                  className={cn(
                    "mr-2 h-4 w-4",
                    selectedCustomer?.id === customer.id ? "opacity-100" : "opacity-0"
                  )}
                />
                <div className="flex flex-col">
                  <span className="font-medium">{customer.name}</span>
                  {(customer.city || customer.phone) && (
                    <span className="text-sm text-muted-foreground">
                      {[customer.city, customer.phone].filter(Boolean).join(' • ')}
                    </span>
                  )}
                </div>
              </CommandItem>
            ))}
          </CommandGroup>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
