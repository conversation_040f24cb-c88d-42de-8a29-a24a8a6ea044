'use client';

import React from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { SalesItem } from './sales-pipeline-board';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import { 
  Building, 
  User, 
  Phone, 
  Calendar, 
  DollarSign, 
  Percent,
  MoreVertical,
  Eye,
  Edit,
  Trash2,
  GripVertical
} from 'lucide-react';
import Link from 'next/link';

interface PipelineCardProps {
  item: SalesItem;
  isActive: boolean;
}

export function PipelineCard({ item, isActive }: PipelineCardProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: item.id,
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  // Get type-specific styling
  const getTypeColor = (type: string) => {
    switch (type) {
      case 'lead':
        return 'bg-blue-50 border-blue-200';
      case 'opportunity':
        return 'bg-green-50 border-green-200';
      case 'prospect':
        return 'bg-purple-50 border-purple-200';
      case 'order':
        return 'bg-orange-50 border-orange-200';
      default:
        return 'bg-gray-50 border-gray-200';
    }
  };

  const getTypeBadgeColor = (type: string) => {
    switch (type) {
      case 'lead':
        return 'bg-blue-100 text-blue-800';
      case 'opportunity':
        return 'bg-green-100 text-green-800';
      case 'prospect':
        return 'bg-purple-100 text-purple-800';
      case 'order':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Get the appropriate link based on type
  const getItemLink = (item: SalesItem) => {
    switch (item.type) {
      case 'lead':
        return `/leads/${item.id}`;
      case 'opportunity':
        return `/sales/opportunities/${item.id}`;
      case 'prospect':
        return `/sales/prospects/${item.id}`;
      case 'order':
        return `/sales/orders/${item.id}`;
      default:
        return '#';
    }
  };

  return (
    <Card
      ref={setNodeRef}
      style={style}
      className={cn(
        'cursor-grab active:cursor-grabbing transition-all duration-200 hover:shadow-md',
        getTypeColor(item.type),
        isDragging && 'opacity-50 shadow-lg scale-105',
        isActive && 'ring-2 ring-primary'
      )}
      {...attributes}
    >
      <CardHeader className="pb-2">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2 mb-1">
              <Badge className={getTypeBadgeColor(item.type)}>
                {item.type.charAt(0).toUpperCase() + item.type.slice(1)}
              </Badge>
              {item.prospectPercentage && (
                <Badge variant="outline" className="text-xs">
                  <Percent className="h-3 w-3 mr-1" />
                  {item.prospectPercentage}%
                </Badge>
              )}
            </div>
            <h4 className="font-medium text-sm text-black truncate">
              {item.customer.name}
            </h4>
            {item.customer.city && (
              <p className="text-xs text-gray-500 truncate">
                <Building className="h-3 w-3 inline mr-1" />
                {item.customer.city}
              </p>
            )}
          </div>
          <div className="flex items-center space-x-1">
            <div
              {...listeners}
              className="p-1 hover:bg-gray-100 rounded cursor-grab active:cursor-grabbing"
            >
              <GripVertical className="h-4 w-4 text-gray-400" />
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                  <MoreVertical className="h-3 w-3" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem asChild>
                  <Link href={getItemLink(item)}>
                    <Eye className="mr-2 h-4 w-4" />
                    View Details
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href={`${getItemLink(item)}/edit`}>
                    <Edit className="mr-2 h-4 w-4" />
                    Edit
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem className="text-red-600">
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0 pb-3">
        <div className="space-y-2">
          {/* Executive */}
          <div className="flex items-center text-xs text-gray-600">
            <User className="h-3 w-3 mr-1" />
            <span className="truncate">{item.executive.name}</span>
          </div>

          {/* Contact Person */}
          {item.contactPerson && (
            <div className="flex items-center text-xs text-gray-600">
              <Phone className="h-3 w-3 mr-1" />
              <span className="truncate">{item.contactPerson}</span>
            </div>
          )}

          {/* Amount */}
          {item.amount && (
            <div className="flex items-center text-xs text-gray-600">
              <DollarSign className="h-3 w-3 mr-1" />
              <span className="font-medium">
                ${item.amount.toLocaleString()}
              </span>
            </div>
          )}

          {/* Follow Up Date */}
          {item.followUpDate && (
            <div className="flex items-center text-xs text-gray-600">
              <Calendar className="h-3 w-3 mr-1" />
              <span>
                Follow up: {format(new Date(item.followUpDate), 'MMM dd')}
              </span>
            </div>
          )}

          {/* Remarks */}
          {item.remarks && (
            <div className="text-xs text-gray-500 line-clamp-2">
              {item.remarks}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
