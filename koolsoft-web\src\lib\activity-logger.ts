import { NextRequest } from 'next/server';
import { getActivityLogService } from '@/lib/services/activity-log.service';

interface LogActivityParams {
  action: string;
  entityType?: string;
  entityId?: string;
  details?: any;
  request?: NextRequest | null;
}

/**
 * Log an activity
 * 
 * This is a convenience function that wraps the activity log service
 * for easier use in API routes and other server-side code.
 */
export async function logActivity({
  action,
  entityType,
  entityId,
  details,
  request
}: LogActivityParams): Promise<void> {
  try {
    const activityLogService = getActivityLogService();
    
    if (request) {
      // Use the API request logging method if request is provided
      await activityLogService.logApiRequest(
        request,
        action,
        entityType,
        entityId,
        details
      );
    } else {
      // Use the general action logging method
      await activityLogService.logAction(
        action,
        undefined, // userId will be determined by the service
        entityType,
        entityId,
        details
      );
    }
  } catch (error) {
    // Don't throw errors for logging failures to avoid breaking the main functionality
    console.error('Failed to log activity:', error);
  }
}
