'use client';

import { DashboardLayout } from '@/components/layout';
import { usePathname } from 'next/navigation';
import { Users } from 'lucide-react';
import { BreadcrumbItemType } from '@/components/layout/page-header';

/**
 * Lead Management Layout Component
 *
 * This component provides a consistent layout for all lead management pages
 * using the standardized DashboardLayout component with collapsible sidebar.
 */
export default function LeadsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();

  // Determine the current page title based on the pathname
  let pageTitle = 'Lead Management';
  if (pathname && pathname !== '/leads') {
    if (pathname.includes('/edit')) {
      pageTitle = 'Edit Lead';
    } else if (pathname.includes('/new')) {
      pageTitle = 'New Lead';
    } else if (pathname.includes('/details')) {
      pageTitle = 'Lead Details';
    }
  }

  // Define breadcrumbs for the page
  const breadcrumbs: BreadcrumbItemType[] = [
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'Lead Management', href: '/leads', icon: <Users className="h-4 w-4" /> }
  ];

  // Add additional breadcrumb for subpages
  if (pathname && pathname !== '/leads') {
    breadcrumbs.push({ label: pageTitle, current: true });
  } else {
    breadcrumbs[breadcrumbs.length - 1].current = true;
  }

  return (
    <DashboardLayout
      title={pageTitle}
      requireAuth={true}
      allowedRoles={['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER']}
      breadcrumbs={breadcrumbs}
    >
      {children}
    </DashboardLayout>
  );
}
