'use client';

import { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  ArrowLeft, 
  Edit, 
  RefreshCw, 
  Calendar, 
  DollarSign, 
  User, 
  Building, 
  Settings,
  FileText,
  CreditCard,
  Wrench
} from 'lucide-react';
import Link from 'next/link';
import { format } from 'date-fns';
import { showErrorToast } from '@/lib/toast';
import { Skeleton } from '@/components/ui/skeleton';
import { AmcConversionActions } from '@/components/amc/amc-conversion-actions';

interface AMCContract {
  id: string;
  customerId: string;
  customer: {
    id: string;
    name: string;
    email?: string;
    phone?: string;
    address?: string;
  };
  contactPersonId?: string;
  contactPerson?: {
    id: string;
    name: string;
    email?: string;
    phone?: string;
  };
  executiveId?: string;
  executive?: {
    id: string;
    name: string;
    email?: string;
  };
  natureOfService?: string;
  startDate: string;
  endDate: string;
  warningDate?: string;
  amount: number;
  numberOfServices?: number;
  numberOfMachines?: number;
  totalTonnage?: number;
  status: string;
  contractNumber?: string;
  remarks?: string;
  createdAt: string;
  updatedAt: string;
  machines?: any[];
  divisions?: any[];
  payments?: any[];
  serviceDates?: any[];
}

/**
 * AMC Contract Detail Page
 * 
 * This page displays comprehensive information about a specific AMC contract
 * including contract details, machines, payments, service dates, and divisions.
 */
export default function AMCContractDetailPage() {
  const params = useParams();
  const router = useRouter();
  const [contract, setContract] = useState<AMCContract | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const contractId = params?.id as string;

  // Fetch contract details
  useEffect(() => {
    const fetchContract = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const response = await fetch(`/api/amc/contracts/${contractId}`, {
          credentials: 'include',
        });

        if (!response.ok) {
          if (response.status === 404) {
            throw new Error('AMC contract not found');
          }
          throw new Error('Failed to fetch contract details');
        }

        const data = await response.json();
        setContract(data);
      } catch (error) {
        console.error('Error fetching contract:', error);
        const errorMessage = error instanceof Error ? error.message : 'Failed to fetch contract details';
        setError(errorMessage);
        showErrorToast('Error', errorMessage);
      } finally {
        setIsLoading(false);
      }
    };

    if (contractId) {
      fetchContract();
    }
  }, [contractId]);

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status?.toUpperCase()) {
      case 'ACTIVE':
        return 'bg-green-100 text-green-800';
      case 'EXPIRED':
        return 'bg-red-100 text-red-800';
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800';
      case 'CANCELLED':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader className="pb-3 bg-primary text-white">
            <div className="flex items-center justify-between">
              <div>
                <Skeleton className="h-6 w-48 bg-white/20" />
                <Skeleton className="h-4 w-64 bg-white/20 mt-2" />
              </div>
              <Skeleton className="h-10 w-24 bg-white/20" />
            </div>
          </CardHeader>
          <CardContent className="p-6">
            <div className="space-y-4">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error || !contract) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader className="pb-3 bg-destructive text-white">
            <CardTitle>Error Loading Contract</CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="text-center py-8 space-y-4">
              <p className="text-black">{error || 'Contract not found'}</p>
              <div className="flex justify-center space-x-4">
                <Button asChild variant="outline">
                  <Link href="/amc">
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Back to AMC List
                  </Link>
                </Button>
                <Button onClick={() => window.location.reload()}>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Retry
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Contract Header */}
      <Card>
        <CardHeader className="pb-3 flex flex-row items-center justify-between bg-primary text-white">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <FileText className="h-5 w-5" />
              <span>Contract #{contract.contractNumber || contract.id.slice(0, 8)}</span>
              <Badge className={`ml-2 ${getStatusColor(contract.status)}`}>
                {contract.status}
              </Badge>
            </CardTitle>
            <CardDescription className="text-gray-100">
              {contract.customer.name} • {format(new Date(contract.startDate), 'MMM dd, yyyy')} - {format(new Date(contract.endDate), 'MMM dd, yyyy')}
            </CardDescription>
          </div>
          <div className="flex space-x-2">
            <AmcConversionActions
              amc={{
                id: contract.id,
                customerId: contract.customerId,
                customerName: contract.customer.name,
                contractNumber: contract.contractNumber,
                numberOfMachines: contract.numberOfMachines,
                executiveId: contract.executiveId,
                contactPersonId: contract.contactPersonId,
                endDate: contract.endDate,
                status: contract.status,
              }}
              variant="button"
              size="sm"
              onConversionSuccess={() => {
                // Refresh contract data after conversion
                window.location.reload();
              }}
            />
            <Button asChild variant="secondary">
              <Link href={`/amc/contracts/${contract.id}/edit`}>
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Link>
            </Button>
            <Button asChild variant="secondary">
              <Link href={`/amc/contracts/${contract.id}/renew`}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Renew
              </Link>
            </Button>
            <Button asChild variant="secondary">
              <Link href="/amc">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to List
              </Link>
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* Contract Details Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="machines">Machines</TabsTrigger>
          <TabsTrigger value="payments">Payments</TabsTrigger>
          <TabsTrigger value="services">Services</TabsTrigger>
          <TabsTrigger value="divisions">Divisions</TabsTrigger>
          <TabsTrigger value="history">History</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Contract Information */}
            <Card>
              <CardHeader className="pb-3 bg-primary text-white">
                <CardTitle className="flex items-center space-x-2">
                  <FileText className="h-4 w-4" />
                  <span>Contract Details</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="p-4 space-y-3">
                <div>
                  <span className="text-sm font-medium text-black">Amount:</span>
                  <p className="text-lg font-semibold text-black">₹{contract.amount.toLocaleString()}</p>
                </div>
                <div>
                  <span className="text-sm font-medium text-black">Duration:</span>
                  <p className="text-black">{format(new Date(contract.startDate), 'MMM dd, yyyy')} - {format(new Date(contract.endDate), 'MMM dd, yyyy')}</p>
                </div>
                <div>
                  <span className="text-sm font-medium text-black">Services:</span>
                  <p className="text-black">{contract.numberOfServices || 'N/A'}</p>
                </div>
                <div>
                  <span className="text-sm font-medium text-black">Machines:</span>
                  <p className="text-black">{contract.numberOfMachines || contract.machines?.length || 0}</p>
                </div>
              </CardContent>
            </Card>

            {/* Customer Information */}
            <Card>
              <CardHeader className="pb-3 bg-primary text-white">
                <CardTitle className="flex items-center space-x-2">
                  <Building className="h-4 w-4" />
                  <span>Customer</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="p-4 space-y-3">
                <div>
                  <span className="text-sm font-medium text-black">Name:</span>
                  <p className="text-black">{contract.customer.name}</p>
                </div>
                {contract.customer.email && (
                  <div>
                    <span className="text-sm font-medium text-black">Email:</span>
                    <p className="text-black">{contract.customer.email}</p>
                  </div>
                )}
                {contract.customer.phone && (
                  <div>
                    <span className="text-sm font-medium text-black">Phone:</span>
                    <p className="text-black">{contract.customer.phone}</p>
                  </div>
                )}
                {contract.customer.address && (
                  <div>
                    <span className="text-sm font-medium text-black">Address:</span>
                    <p className="text-black">{contract.customer.address}</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Executive Information */}
            <Card>
              <CardHeader className="pb-3 bg-primary text-white">
                <CardTitle className="flex items-center space-x-2">
                  <User className="h-4 w-4" />
                  <span>Executive</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="p-4 space-y-3">
                {contract.executive ? (
                  <>
                    <div>
                      <span className="text-sm font-medium text-black">Name:</span>
                      <p className="text-black">{contract.executive.name}</p>
                    </div>
                    {contract.executive.email && (
                      <div>
                        <span className="text-sm font-medium text-black">Email:</span>
                        <p className="text-black">{contract.executive.email}</p>
                      </div>
                    )}
                  </>
                ) : (
                  <p className="text-black">No executive assigned</p>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Additional Details */}
          {(contract.natureOfService || contract.remarks) && (
            <Card>
              <CardHeader className="pb-3 bg-primary text-white">
                <CardTitle>Additional Information</CardTitle>
              </CardHeader>
              <CardContent className="p-4 space-y-4">
                {contract.natureOfService && (
                  <div>
                    <span className="text-sm font-medium text-black">Nature of Service:</span>
                    <p className="text-black mt-1">{contract.natureOfService}</p>
                  </div>
                )}
                {contract.remarks && (
                  <div>
                    <span className="text-sm font-medium text-black">Remarks:</span>
                    <p className="text-black mt-1">{contract.remarks}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Machines Tab */}
        <TabsContent value="machines">
          <Card>
            <CardHeader className="pb-3 bg-primary text-white flex flex-row items-center justify-between">
              <CardTitle className="flex items-center space-x-2">
                <Settings className="h-4 w-4" />
                <span>Machines ({contract.machines?.length || 0})</span>
              </CardTitle>
              <Button asChild variant="secondary" size="sm">
                <Link href={`/amc/machines?contractId=${contract.id}`}>
                  <Wrench className="h-4 w-4 mr-2" />
                  Manage Machines
                </Link>
              </Button>
            </CardHeader>
            <CardContent className="p-6">
              {contract.machines && contract.machines.length > 0 ? (
                <div className="space-y-4">
                  {contract.machines.map((machine: any, index: number) => (
                    <div key={machine.id || index} className="border rounded-lg p-4">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <span className="text-sm font-medium text-black">Serial Number:</span>
                          <p className="text-black">{machine.serialNumber || 'N/A'}</p>
                        </div>
                        <div>
                          <span className="text-sm font-medium text-black">Location:</span>
                          <p className="text-black">{machine.location || 'N/A'}</p>
                        </div>
                        <div>
                          <span className="text-sm font-medium text-black">Brand/Model:</span>
                          <p className="text-black">{machine.brand?.name || 'N/A'} / {machine.model?.name || 'N/A'}</p>
                        </div>
                      </div>
                      {machine.installationDate && (
                        <div className="mt-2">
                          <span className="text-sm font-medium text-black">Installation Date:</span>
                          <p className="text-black">{format(new Date(machine.installationDate), 'MMM dd, yyyy')}</p>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Settings className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-black">No machines assigned to this contract</p>
                  <Button asChild className="mt-4" variant="outline">
                    <Link href={`/amc/machines?contractId=${contract.id}`}>
                      Add Machines
                    </Link>
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="payments">
          <Card>
            <CardHeader className="pb-3 bg-primary text-white flex flex-row items-center justify-between">
              <CardTitle className="flex items-center space-x-2">
                <CreditCard className="h-4 w-4" />
                <span>Payments ({contract.payments?.length || 0})</span>
              </CardTitle>
              <Button asChild variant="secondary" size="sm">
                <Link href={`/amc/contracts/${contract.id}/payments`}>
                  <CreditCard className="h-4 w-4 mr-2" />
                  Manage Payments
                </Link>
              </Button>
            </CardHeader>
            <CardContent className="p-6">
              {contract.payments && contract.payments.length > 0 ? (
                <div className="space-y-4">
                  {/* Payment Summary */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                      <div className="flex items-center space-x-2">
                        <DollarSign className="h-5 w-5 text-green-600" />
                        <span className="text-sm font-medium text-black">Total Paid</span>
                      </div>
                      <p className="text-lg font-semibold text-black mt-1">
                        ₹{contract.payments.reduce((sum: number, payment: any) => sum + (payment.amount || 0), 0).toLocaleString()}
                      </p>
                    </div>
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <div className="flex items-center space-x-2">
                        <DollarSign className="h-5 w-5 text-blue-600" />
                        <span className="text-sm font-medium text-black">Contract Amount</span>
                      </div>
                      <p className="text-lg font-semibold text-black mt-1">₹{contract.amount.toLocaleString()}</p>
                    </div>
                    <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                      <div className="flex items-center space-x-2">
                        <DollarSign className="h-5 w-5 text-orange-600" />
                        <span className="text-sm font-medium text-black">Balance</span>
                      </div>
                      <p className="text-lg font-semibold text-black mt-1">
                        ₹{(contract.amount - contract.payments.reduce((sum: number, payment: any) => sum + (payment.amount || 0), 0)).toLocaleString()}
                      </p>
                    </div>
                  </div>

                  <Separator />

                  {/* Payment List */}
                  <div className="space-y-3">
                    {contract.payments.map((payment: any, index: number) => (
                      <div key={payment.id || index} className="border rounded-lg p-4">
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                          <div>
                            <span className="text-sm font-medium text-black">Amount:</span>
                            <p className="text-black font-semibold">₹{payment.amount?.toLocaleString() || 'N/A'}</p>
                          </div>
                          <div>
                            <span className="text-sm font-medium text-black">Date:</span>
                            <p className="text-black">{payment.paymentDate ? format(new Date(payment.paymentDate), 'MMM dd, yyyy') : 'N/A'}</p>
                          </div>
                          <div>
                            <span className="text-sm font-medium text-black">Mode:</span>
                            <p className="text-black">{payment.paymentMode || 'N/A'}</p>
                          </div>
                          <div>
                            <span className="text-sm font-medium text-black">Receipt:</span>
                            <p className="text-black">{payment.receiptNumber || 'N/A'}</p>
                          </div>
                        </div>
                        {payment.remarks && (
                          <div className="mt-2">
                            <span className="text-sm font-medium text-black">Remarks:</span>
                            <p className="text-black text-sm">{payment.remarks}</p>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <CreditCard className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-black">No payments recorded for this contract</p>
                  <Button asChild className="mt-4" variant="outline">
                    <Link href={`/amc/contracts/${contract.id}/payments`}>
                      Add Payment
                    </Link>
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="services">
          <Card>
            <CardHeader className="pb-3 bg-primary text-white">
              <CardTitle className="flex items-center space-x-2">
                <Calendar className="h-4 w-4" />
                <span>Service Schedule ({contract.serviceDates?.length || 0})</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              {contract.serviceDates && contract.serviceDates.length > 0 ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                      <span className="text-sm font-medium text-black">Completed</span>
                      <p className="text-lg font-semibold text-black mt-1">
                        {contract.serviceDates.filter((service: any) => service.status === 'COMPLETED').length}
                      </p>
                    </div>
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <span className="text-sm font-medium text-black">Scheduled</span>
                      <p className="text-lg font-semibold text-black mt-1">
                        {contract.serviceDates.filter((service: any) => service.status === 'SCHEDULED').length}
                      </p>
                    </div>
                    <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                      <span className="text-sm font-medium text-black">Missed</span>
                      <p className="text-lg font-semibold text-black mt-1">
                        {contract.serviceDates.filter((service: any) => service.status === 'MISSED').length}
                      </p>
                    </div>
                  </div>

                  <Separator />

                  <div className="space-y-3">
                    {contract.serviceDates.map((service: any, index: number) => (
                      <div key={service.id || index} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between">
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 flex-1">
                            <div>
                              <span className="text-sm font-medium text-black">Service #{service.serviceNumber || index + 1}</span>
                              <p className="text-black">{service.serviceDate ? format(new Date(service.serviceDate), 'MMM dd, yyyy') : 'N/A'}</p>
                            </div>
                            <div>
                              <span className="text-sm font-medium text-black">Status:</span>
                              <Badge className={`ml-2 ${
                                service.status === 'COMPLETED' ? 'bg-green-100 text-green-800' :
                                service.status === 'SCHEDULED' ? 'bg-blue-100 text-blue-800' :
                                'bg-red-100 text-red-800'
                              }`}>
                                {service.status || 'SCHEDULED'}
                              </Badge>
                            </div>
                            <div>
                              <span className="text-sm font-medium text-black">Service Type:</span>
                              <p className="text-black">{service.serviceType || 'Regular Maintenance'}</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-black">No service dates scheduled for this contract</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="divisions">
          <Card>
            <CardHeader className="pb-3 bg-primary text-white">
              <CardTitle className="flex items-center space-x-2">
                <Building className="h-4 w-4" />
                <span>Division Assignment ({contract.divisions?.length || 0})</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              {contract.divisions && contract.divisions.length > 0 ? (
                <div className="space-y-4">
                  {/* Division Summary */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <span className="text-sm font-medium text-black">Total Assigned</span>
                      <p className="text-lg font-semibold text-black mt-1">
                        {contract.divisions.reduce((sum: number, division: any) => sum + (division.percentage || 0), 0)}%
                      </p>
                    </div>
                    <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                      <span className="text-sm font-medium text-black">Primary Division</span>
                      <p className="text-lg font-semibold text-black mt-1">
                        {contract.divisions.find((d: any) => d.isPrimary)?.division?.name || 'None'}
                      </p>
                    </div>
                  </div>

                  <Separator />

                  {/* Division List */}
                  <div className="space-y-3">
                    {contract.divisions.map((divisionAssignment: any, index: number) => (
                      <div key={divisionAssignment.id || index} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between">
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 flex-1">
                            <div>
                              <span className="text-sm font-medium text-black">Division:</span>
                              <p className="text-black font-semibold">{divisionAssignment.division?.name || 'Unknown Division'}</p>
                            </div>
                            <div>
                              <span className="text-sm font-medium text-black">Percentage:</span>
                              <p className="text-black font-semibold">{divisionAssignment.percentage || 0}%</p>
                            </div>
                            <div>
                              <span className="text-sm font-medium text-black">Role:</span>
                              <Badge className={`ml-2 ${
                                divisionAssignment.isPrimary ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                              }`}>
                                {divisionAssignment.isPrimary ? 'Primary' : 'Supporting'}
                              </Badge>
                            </div>
                          </div>
                        </div>
                        {divisionAssignment.division?.description && (
                          <div className="mt-2">
                            <span className="text-sm font-medium text-black">Description:</span>
                            <p className="text-black text-sm">{divisionAssignment.division.description}</p>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>

                  {/* Revenue Breakdown */}
                  <div className="mt-6">
                    <h4 className="text-sm font-medium text-black mb-3">Revenue Allocation</h4>
                    <div className="space-y-2">
                      {contract.divisions.map((divisionAssignment: any, index: number) => (
                        <div key={index} className="flex justify-between items-center py-2 border-b">
                          <span className="text-black">{divisionAssignment.division?.name || 'Unknown'}</span>
                          <span className="text-black font-semibold">
                            ₹{((contract.amount * (divisionAssignment.percentage || 0)) / 100).toLocaleString()}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <Building className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-black">No divisions assigned to this contract</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history">
          <Card>
            <CardHeader className="pb-3 bg-primary text-white">
              <CardTitle className="flex items-center space-x-2">
                <FileText className="h-4 w-4" />
                <span>Contract History</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              <div className="space-y-4">
                {/* Contract Timeline */}
                <div className="space-y-4">
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0 w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-black">Contract Created</span>
                        <span className="text-sm text-black">{format(new Date(contract.createdAt), 'MMM dd, yyyy HH:mm')}</span>
                      </div>
                      <p className="text-sm text-black mt-1">Contract was created and activated</p>
                    </div>
                  </div>

                  {contract.updatedAt !== contract.createdAt && (
                    <div className="flex items-start space-x-4">
                      <div className="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium text-black">Contract Updated</span>
                          <span className="text-sm text-black">{format(new Date(contract.updatedAt), 'MMM dd, yyyy HH:mm')}</span>
                        </div>
                        <p className="text-sm text-black mt-1">Contract details were modified</p>
                      </div>
                    </div>
                  )}

                  {contract.status === 'EXPIRED' && (
                    <div className="flex items-start space-x-4">
                      <div className="flex-shrink-0 w-2 h-2 bg-red-500 rounded-full mt-2"></div>
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium text-black">Contract Expired</span>
                          <span className="text-sm text-black">{format(new Date(contract.endDate), 'MMM dd, yyyy')}</span>
                        </div>
                        <p className="text-sm text-black mt-1">Contract reached its end date</p>
                      </div>
                    </div>
                  )}
                </div>

                <Separator />

                {/* Contract Details Summary */}
                <div>
                  <h4 className="text-sm font-medium text-black mb-3">Contract Summary</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <span className="text-sm font-medium text-black">Contract Duration:</span>
                      <p className="text-black">
                        {Math.ceil((new Date(contract.endDate).getTime() - new Date(contract.startDate).getTime()) / (1000 * 60 * 60 * 24))} days
                      </p>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-black">Days Remaining:</span>
                      <p className="text-black">
                        {contract.status === 'ACTIVE'
                          ? Math.max(0, Math.ceil((new Date(contract.endDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)))
                          : 0
                        } days
                      </p>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-black">Total Machines:</span>
                      <p className="text-black">{contract.machines?.length || 0}</p>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-black">Total Payments:</span>
                      <p className="text-black">{contract.payments?.length || 0}</p>
                    </div>
                  </div>
                </div>

                {/* Performance Metrics */}
                {(contract.serviceDates?.length || 0) > 0 && (
                  <>
                    <Separator />
                    <div>
                      <h4 className="text-sm font-medium text-black mb-3">Service Performance</h4>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <span className="text-sm font-medium text-black">Completion Rate:</span>
                          <p className="text-black">
                            {contract.serviceDates?.length > 0
                              ? Math.round((contract.serviceDates.filter((s: any) => s.status === 'COMPLETED').length / contract.serviceDates.length) * 100)
                              : 0
                            }%
                          </p>
                        </div>
                        <div>
                          <span className="text-sm font-medium text-black">Services Completed:</span>
                          <p className="text-black">{contract.serviceDates?.filter((s: any) => s.status === 'COMPLETED').length || 0}</p>
                        </div>
                        <div>
                          <span className="text-sm font-medium text-black">Services Pending:</span>
                          <p className="text-black">{contract.serviceDates?.filter((s: any) => s.status === 'SCHEDULED').length || 0}</p>
                        </div>
                      </div>
                    </div>
                  </>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
