import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { ConversionReportRepository } from '@/lib/repositories/conversion-report.repository';
import { conversionReportFilterSchema } from '@/lib/validations/conversion.schema';
import { logActivity } from '@/lib/activity-logger';

/**
 * GET /api/conversions/reports/trends
 * Get time-based conversion trends
 */
async function trendsHandler(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Parse and validate filters
    const period = (searchParams.get('period') || 'monthly') as 'daily' | 'weekly' | 'monthly' | 'quarterly';
    const filters = {
      dateFrom: searchParams.get('dateFrom') ? new Date(searchParams.get('dateFrom')!) : undefined,
      dateTo: searchParams.get('dateTo') ? new Date(searchParams.get('dateTo')!) : undefined,
      conversionType: searchParams.get('conversionType') || undefined,
      customerId: searchParams.get('customerId') || undefined,
      status: searchParams.get('status') || undefined,
      userId: searchParams.get('userId') || undefined,
      amountMin: searchParams.get('amountMin') ? parseFloat(searchParams.get('amountMin')!) : undefined,
      amountMax: searchParams.get('amountMax') ? parseFloat(searchParams.get('amountMax')!) : undefined,
    };

    const validatedFilters = conversionReportFilterSchema.parse(filters);
    
    // Get trends
    const repository = new ConversionReportRepository();
    const trends = await repository.getConversionTrends(period, validatedFilters);

    // Log the activity
    await logActivity({
      action: 'VIEW',
      entityType: 'CONVERSION_REPORT',
      details: `Viewed conversion trends (${period})`,
      request
    });

    return NextResponse.json(trends);
  } catch (error) {
    console.error('Error fetching conversion trends:', error);
    return NextResponse.json(
      { error: 'Failed to fetch conversion trends' },
      { status: 500 }
    );
  }
}

export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  trendsHandler
);
