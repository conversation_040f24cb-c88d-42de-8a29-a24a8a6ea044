'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ArrowRightLeft, ChevronDown, Shield, AlertTriangle } from 'lucide-react';
import { AmcToOutWarrantyConversionDialog } from './amc-to-out-warranty-conversion-dialog';
import { showErrorToast } from '@/lib/toast';

interface AMCContract {
  id: string;
  customerId: string;
  customerName?: string;
  contractNumber?: string;
  numberOfMachines?: number;
  executiveId?: string;
  contactPersonId?: string;
  endDate: string;
  status: string;
}

interface AmcConversionActionsProps {
  amc: AMCContract;
  variant?: 'button' | 'dropdown' | 'inline';
  size?: 'sm' | 'default' | 'lg';
  onConversionSuccess?: () => void;
}

export function AmcConversionActions({
  amc,
  variant = 'dropdown',
  size = 'default',
  onConversionSuccess,
}: AmcConversionActionsProps) {
  const [showOutWarrantyConversion, setShowOutWarrantyConversion] = useState(false);
  const [isValidating, setIsValidating] = useState(false);

  const handleOutWarrantyConversion = async () => {
    if (amc.status === 'CONVERTED') {
      showErrorToast('This AMC contract has already been converted');
      return;
    }

    if (amc.status === 'CANCELLED') {
      showErrorToast('Cannot convert a cancelled AMC contract');
      return;
    }

    setIsValidating(true);
    try {
      // Validate conversion eligibility
      const response = await fetch('/api/conversions/validate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          sourceId: amc.id,
          conversionType: 'AMC_TO_OUT_WARRANTY',
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to validate conversion');
      }

      const validation = await response.json();
      if (!validation.valid) {
        showErrorToast(validation.reason || 'AMC is not eligible for conversion');
        return;
      }

      setShowOutWarrantyConversion(true);
    } catch (error) {
      console.error('Error validating AMC conversion:', error);
      showErrorToast(error instanceof Error ? error.message : 'Failed to validate conversion');
    } finally {
      setIsValidating(false);
    }
  };

  if (variant === 'button') {
    return (
      <>
        <Button
          onClick={handleOutWarrantyConversion}
          disabled={isValidating}
          size={size}
          className="bg-primary hover:bg-primary/90"
        >
          <ArrowRightLeft className="w-4 h-4 mr-2" />
          Convert to Out-Warranty
        </Button>
        
        <AmcToOutWarrantyConversionDialog
          open={showOutWarrantyConversion}
          onOpenChange={setShowOutWarrantyConversion}
          amcId={amc.id}
          amcDetails={{
            id: amc.id,
            customerId: amc.customerId,
            customerName: amc.customerName || 'Unknown Customer',
            contractNumber: amc.contractNumber,
            numberOfMachines: amc.numberOfMachines,
            executiveId: amc.executiveId,
            contactPersonId: amc.contactPersonId,
            endDate: amc.endDate,
          }}
          onSuccess={onConversionSuccess}
        />
      </>
    );
  }

  if (variant === 'inline') {
    return (
      <div className="flex items-center gap-2">
        <Button
          onClick={handleOutWarrantyConversion}
          disabled={isValidating}
          size={size}
          variant="outline"
        >
          <AlertTriangle className="w-4 h-4 mr-2" />
          To Out-Warranty
        </Button>
        
        <AmcToOutWarrantyConversionDialog
          open={showOutWarrantyConversion}
          onOpenChange={setShowOutWarrantyConversion}
          amcId={amc.id}
          amcDetails={{
            id: amc.id,
            customerId: amc.customerId,
            customerName: amc.customerName || 'Unknown Customer',
            contractNumber: amc.contractNumber,
            numberOfMachines: amc.numberOfMachines,
            executiveId: amc.executiveId,
            contactPersonId: amc.contactPersonId,
            endDate: amc.endDate,
          }}
          onSuccess={onConversionSuccess}
        />
      </div>
    );
  }

  // Default dropdown variant
  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size={size} disabled={isValidating}>
            <ArrowRightLeft className="w-4 h-4 mr-2" />
            Convert
            <ChevronDown className="w-4 h-4 ml-2" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem
            onClick={handleOutWarrantyConversion}
            disabled={isValidating}
          >
            <AlertTriangle className="w-4 h-4 mr-2" />
            Convert to Out-of-Warranty
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <AmcToOutWarrantyConversionDialog
        open={showOutWarrantyConversion}
        onOpenChange={setShowOutWarrantyConversion}
        amcId={amc.id}
        amcDetails={{
          id: amc.id,
          customerId: amc.customerId,
          customerName: amc.customerName || 'Unknown Customer',
          contractNumber: amc.contractNumber,
          numberOfMachines: amc.numberOfMachines,
          executiveId: amc.executiveId,
          contactPersonId: amc.contactPersonId,
          endDate: amc.endDate,
        }}
        onSuccess={onConversionSuccess}
      />
    </>
  );
}
