import { PrismaClient, Prisma } from '@/generated/prisma';
import { BaseRepository } from './base.repository';
import { PrismaRepository } from './prisma.repository';

/**
 * Sales Opportunity Repository
 *
 * This repository handles database operations for the Sales Opportunity entity.
 * It provides methods for CRUD operations and specialized queries.
 */
export class SalesOpportunityRepository extends PrismaRepository<
  Prisma.sales_opportunitiesGetPayload<{
    include: {
      customer: true;
      executive: true;
    };
  }>,
  string,
  Prisma.sales_opportunitiesCreateInput,
  Prisma.sales_opportunitiesUpdateInput
> {
  constructor(prismaClient?: PrismaClient) {
    super('sales_opportunities');
    if (prismaClient) {
      this.prisma = prismaClient;
    }
  }

  protected createTransactionRepository(tx: any): SalesOpportunityRepository {
    return new SalesOpportunityRepository(tx);
  }

  /**
   * Find sales opportunities with filtering, pagination, and sorting
   * @param filters Filter criteria
   * @returns Promise resolving to paginated sales opportunities with metadata
   */
  async findWithFilters(filters: {
    customerId?: string;
    executiveId?: string;
    status?: string;
    startDate?: Date;
    endDate?: Date;
    search?: string;
    skip?: number;
    take?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }) {
    const {
      customerId,
      executiveId,
      status,
      startDate,
      endDate,
      search,
      skip = 0,
      take = 10,
      sortBy = 'opportunityDate',
      sortOrder = 'desc',
    } = filters;

    // Build where clause
    const where: Prisma.sales_opportunitiesWhereInput = {};

    if (customerId) {
      where.customerId = customerId;
    }

    if (executiveId) {
      where.executiveId = executiveId;
    }

    if (status) {
      where.status = status;
    }

    if (startDate || endDate) {
      where.opportunityDate = {};
      if (startDate) {
        where.opportunityDate.gte = startDate;
      }
      if (endDate) {
        where.opportunityDate.lte = endDate;
      }
    }

    if (search) {
      where.OR = [
        {
          customer: {
            name: {
              contains: search,
              mode: 'insensitive',
            },
          },
        },
        {
          contactPerson: {
            contains: search,
            mode: 'insensitive',
          },
        },
        {
          contactPhone: {
            contains: search,
            mode: 'insensitive',
          },
        },
        {
          remarks: {
            contains: search,
            mode: 'insensitive',
          },
        },
      ];
    }

    // Build order by clause
    let orderBy: Prisma.sales_opportunitiesOrderByWithRelationInput = {};
    if (sortBy === 'customer') {
      orderBy = { customer: { name: sortOrder } };
    } else if (sortBy === 'executive') {
      orderBy = { executive: { name: sortOrder } };
    } else if (sortBy === 'opportunityDate') {
      orderBy = { opportunityDate: sortOrder };
    } else if (sortBy === 'status') {
      orderBy = { status: sortOrder };
    } else if (sortBy === 'prospectPercentage') {
      orderBy = { prospectPercentage: sortOrder };
    } else if (sortBy === 'followUpDate') {
      orderBy = { followUpDate: sortOrder };
    } else if (sortBy === 'nextVisitDate') {
      orderBy = { nextVisitDate: sortOrder };
    } else {
      orderBy = { opportunityDate: sortOrder }; // Default fallback
    }

    // Execute queries
    const [opportunities, total] = await Promise.all([
      this.model.findMany({
        where,
        include: {
          customer: {
            select: {
              id: true,
              name: true,
              email: true,
              phone: true,
              city: true,
            },
          },
          executive: {
            select: {
              id: true,
              name: true,
              email: true,
              designation: true,
            },
          },
        },
        orderBy,
        skip,
        take,
      }),
      this.model.count({ where }),
    ]);

    return {
      data: opportunities,
      pagination: {
        total,
        skip,
        take,
        pages: Math.ceil(total / take),
      },
    };
  }

  /**
   * Find sales opportunities by customer ID
   * @param customerId Customer ID
   * @param options Query options
   * @returns Promise resolving to sales opportunities
   */
  async findByCustomerId(
    customerId: string,
    options?: {
      skip?: number;
      take?: number;
      includeRelations?: boolean;
    }
  ) {
    const { skip = 0, take = 10, includeRelations = true } = options || {};

    return this.model.findMany({
      where: { customerId },
      include: includeRelations
        ? {
            customer: {
              select: {
                id: true,
                name: true,
                email: true,
                phone: true,
                city: true,
              },
            },
            executive: {
              select: {
                id: true,
                name: true,
                email: true,
                designation: true,
              },
            },
          }
        : undefined,
      orderBy: { opportunityDate: 'desc' },
      skip,
      take,
    });
  }

  /**
   * Find sales opportunities by executive ID
   * @param executiveId Executive ID
   * @param options Query options
   * @returns Promise resolving to sales opportunities
   */
  async findByExecutiveId(
    executiveId: string,
    options?: {
      skip?: number;
      take?: number;
      includeRelations?: boolean;
    }
  ) {
    const { skip = 0, take = 10, includeRelations = true } = options || {};

    return this.model.findMany({
      where: { executiveId },
      include: includeRelations
        ? {
            customer: {
              select: {
                id: true,
                name: true,
                email: true,
                phone: true,
                city: true,
              },
            },
            executive: {
              select: {
                id: true,
                name: true,
                email: true,
                designation: true,
              },
            },
          }
        : undefined,
      orderBy: { opportunityDate: 'desc' },
      skip,
      take,
    });
  }

  /**
   * Find sales opportunities by status
   * @param status Opportunity status
   * @param options Query options
   * @returns Promise resolving to sales opportunities
   */
  async findByStatus(
    status: string,
    options?: {
      skip?: number;
      take?: number;
      includeRelations?: boolean;
    }
  ) {
    const { skip = 0, take = 10, includeRelations = true } = options || {};

    return this.model.findMany({
      where: { status },
      include: includeRelations
        ? {
            customer: {
              select: {
                id: true,
                name: true,
                email: true,
                phone: true,
                city: true,
              },
            },
            executive: {
              select: {
                id: true,
                name: true,
                email: true,
                designation: true,
              },
            },
          }
        : undefined,
      orderBy: { opportunityDate: 'desc' },
      skip,
      take,
    });
  }

  /**
   * Get sales opportunities statistics
   * @param filters Optional filters
   * @returns Promise resolving to statistics
   */
  async getStatistics(filters?: {
    customerId?: string;
    executiveId?: string;
    startDate?: Date;
    endDate?: Date;
  }) {
    const { customerId, executiveId, startDate, endDate } = filters || {};

    // Build where clause
    const where: Prisma.sales_opportunitiesWhereInput = {};

    if (customerId) {
      where.customerId = customerId;
    }

    if (executiveId) {
      where.executiveId = executiveId;
    }

    if (startDate || endDate) {
      where.opportunityDate = {};
      if (startDate) {
        where.opportunityDate.gte = startDate;
      }
      if (endDate) {
        where.opportunityDate.lte = endDate;
      }
    }

    // Get statistics
    const [
      total,
      openOpportunities,
      qualifiedOpportunities,
      closedWonOpportunities,
      closedLostOpportunities,
      averageProspectPercentage,
    ] = await Promise.all([
      this.model.count({ where }),
      this.model.count({ where: { ...where, status: 'OPEN' } }),
      this.model.count({ where: { ...where, status: 'QUALIFIED' } }),
      this.model.count({ where: { ...where, status: 'CLOSED_WON' } }),
      this.model.count({ where: { ...where, status: 'CLOSED_LOST' } }),
      this.model.aggregate({
        where: {
          ...where,
          prospectPercentage: { not: null },
        },
        _avg: {
          prospectPercentage: true,
        },
      }),
    ]);

    return {
      total,
      openOpportunities,
      qualifiedOpportunities,
      closedWonOpportunities,
      closedLostOpportunities,
      averageProspectPercentage: averageProspectPercentage._avg.prospectPercentage || 0,
      conversionRate: total > 0 ? (closedWonOpportunities / total) * 100 : 0,
    };
  }

  /**
   * Get opportunities requiring follow-up
   * @param date Date to check for follow-ups (defaults to today)
   * @returns Promise resolving to opportunities requiring follow-up
   */
  async getOpportunitiesRequiringFollowUp(date?: Date) {
    const checkDate = date || new Date();
    checkDate.setHours(23, 59, 59, 999); // End of day

    return this.model.findMany({
      where: {
        followUpDate: {
          lte: checkDate,
        },
        status: {
          notIn: ['CLOSED_WON', 'CLOSED_LOST'],
        },
      },
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
            city: true,
          },
        },
        executive: {
          select: {
            id: true,
            name: true,
            email: true,
            designation: true,
          },
        },
      },
      orderBy: { followUpDate: 'asc' },
    });
  }

  /**
   * Update opportunity status
   * @param id Opportunity ID
   * @param status New status
   * @param remarks Optional remarks
   * @returns Promise resolving to updated opportunity
   */
  async updateStatus(id: string, status: string, remarks?: string) {
    const updateData: Prisma.sales_opportunitiesUpdateInput = {
      status,
      updatedAt: new Date(),
    };

    if (remarks) {
      updateData.remarks = remarks;
    }

    return this.model.update({
      where: { id },
      data: updateData,
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
            city: true,
          },
        },
        executive: {
          select: {
            id: true,
            name: true,
            email: true,
            designation: true,
          },
        },
      },
    });
  }
}
