import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { withRoleProtection } from '@/lib/auth/middleware';
import { getHistoryCardRepository } from '@/lib/repositories';

// Validation schemas
const historyCardUpdateSchema = z.object({
  customerId: z.string().uuid('Invalid customer ID').optional(),
  cardNo: z.number().int().positive().optional(),
  source: z.enum(['AMC', 'INW', 'OTW']).optional(),
  amcId: z.string().uuid().optional(),
  inWarrantyId: z.string().uuid().optional(),
  outWarrantyId: z.string().uuid().optional(),
  toCardNo: z.number().int().positive().optional(),
  originalId: z.number().int().positive().optional(),
});

const paramsSchema = z.object({
  id: z.string().uuid('Invalid history card ID'),
});

/**
 * GET /api/history-cards/[id]
 * Retrieve a specific history card with all related data
 */
async function getHistoryCard(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Await and validate parameters
    const resolvedParams = await params;
    const validatedParams = paramsSchema.parse(resolvedParams);
    
    const historyCardRepository = getHistoryCardRepository();
    
    // Get history card with all relations
    const historyCard = await historyCardRepository.findWithRelations(validatedParams.id);
    
    if (!historyCard) {
      return NextResponse.json(
        {
          success: false,
          error: 'History card not found',
        },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      data: historyCard,
    });
  } catch (error) {
    console.error('Error fetching history card:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid parameters',
          details: error.errors,
        },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch history card',
      },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/history-cards/[id]
 * Update a specific history card
 */
async function updateHistoryCard(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Await and validate parameters
    const resolvedParams = await params;
    const validatedParams = paramsSchema.parse(resolvedParams);
    
    const body = await request.json();
    
    // Validate request body
    const validatedData = historyCardUpdateSchema.parse(body);
    
    const historyCardRepository = getHistoryCardRepository();
    
    // Check if history card exists
    const existingHistoryCard = await historyCardRepository.findById(validatedParams.id);
    if (!existingHistoryCard) {
      return NextResponse.json(
        {
          success: false,
          error: 'History card not found',
        },
        { status: 404 }
      );
    }
    
    // Update the history card using the model directly
    const updatedHistoryCard = await (historyCardRepository as any).model.update({
      where: { id: validatedParams.id },
      data: validatedData,
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            address: true,
            city: true,
            phone: true,
          },
        },
        sections: {
          select: {
            id: true,
            sectionCode: true,
            content: true,
            createdAt: true,
            updatedAt: true,
          },
          orderBy: {
            sectionCode: 'asc',
          },
        },
      },
    });
    
    return NextResponse.json({
      success: true,
      data: updatedHistoryCard,
      message: 'History card updated successfully',
    });
  } catch (error) {
    console.error('Error updating history card:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid request data',
          details: error.errors,
        },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update history card',
      },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/history-cards/[id]
 * Delete a specific history card
 */
async function deleteHistoryCard(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Await and validate parameters
    const resolvedParams = await params;
    const validatedParams = paramsSchema.parse(resolvedParams);
    
    const historyCardRepository = getHistoryCardRepository();
    
    // Check if history card exists
    const existingHistoryCard = await historyCardRepository.findById(validatedParams.id);
    if (!existingHistoryCard) {
      return NextResponse.json(
        {
          success: false,
          error: 'History card not found',
        },
        { status: 404 }
      );
    }
    
    // Delete the history card (cascade will handle sections)
    await historyCardRepository.delete(validatedParams.id);
    
    return NextResponse.json({
      success: true,
      message: 'History card deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting history card:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to delete history card',
      },
      { status: 500 }
    );
  }
}

// Export handlers with role protection
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  getHistoryCard
);

export const PUT = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  updateHistoryCard
);

export const DELETE = withRoleProtection(
  ['ADMIN', 'MANAGER'],
  deleteHistoryCard
);
