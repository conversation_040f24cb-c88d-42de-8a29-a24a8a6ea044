'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { 
  ArrowRightLeft, 
  ChevronDown, 
  FileText, 
  Shield, 
  ShieldCheck,
  AlertCircle 
} from 'lucide-react';
import { WarrantyToAmcConversionDialog } from './warranty-to-amc-conversion-dialog';
import { WarrantyToOutWarrantyConversionDialog } from './warranty-to-out-warranty-conversion-dialog';
import { toast } from '@/components/ui/use-toast';

interface WarrantyConversionActionsProps {
  warranty: {
    id: string;
    customerId: string;
    customerName?: string;
    bslNo?: string;
    numberOfMachines: number;
    status: string;
    executiveId?: string;
    contactPersonId?: string;
  };
  onConversionSuccess?: () => void;
  variant?: 'button' | 'dropdown' | 'inline';
  size?: 'sm' | 'default' | 'lg';
}

export function WarrantyConversionActions({
  warranty,
  onConversionSuccess,
  variant = 'dropdown',
  size = 'default',
}: WarrantyConversionActionsProps) {
  const [showAmcConversion, setShowAmcConversion] = useState(false);
  const [showOutWarrantyConversion, setShowOutWarrantyConversion] = useState(false);
  const [isValidating, setIsValidating] = useState(false);

  // Check if warranty can be converted
  const canConvert = warranty.status === 'ACTIVE';

  const validateConversion = async (conversionType: string) => {
    setIsValidating(true);
    try {
      const response = await fetch('/api/conversions/validate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          sourceId: warranty.id,
          conversionType,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to validate conversion');
      }

      const result = await response.json();
      
      if (!result.valid) {
        toast({
          title: 'Conversion Not Available',
          description: result.message,
          variant: 'destructive',
        });
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error validating conversion:', error);
      toast({
        title: 'Validation Error',
        description: error instanceof Error ? error.message : 'Failed to validate conversion',
        variant: 'destructive',
      });
      return false;
    } finally {
      setIsValidating(false);
    }
  };

  const handleAmcConversion = async () => {
    const isValid = await validateConversion('WARRANTY_TO_AMC');
    if (isValid) {
      setShowAmcConversion(true);
    }
  };

  const handleOutWarrantyConversion = async () => {
    const isValid = await validateConversion('WARRANTY_TO_OUT_WARRANTY');
    if (isValid) {
      setShowOutWarrantyConversion(true);
    }
  };

  const getStatusBadge = () => {
    if (warranty.status === 'CONVERTED') {
      return (
        <Badge variant="secondary" className="ml-2">
          <ArrowRightLeft className="w-3 h-3 mr-1" />
          Converted
        </Badge>
      );
    }
    return null;
  };

  if (!canConvert) {
    return (
      <div className="flex items-center">
        <Button variant="outline" size={size} disabled>
          <AlertCircle className="w-4 h-4 mr-2" />
          Cannot Convert
        </Button>
        {getStatusBadge()}
      </div>
    );
  }

  if (variant === 'button') {
    return (
      <>
        <Button
          onClick={handleAmcConversion}
          disabled={isValidating}
          size={size}
          className="bg-primary hover:bg-primary/90"
        >
          <ArrowRightLeft className="w-4 h-4 mr-2" />
          Convert to AMC
        </Button>
        
        <WarrantyToAmcConversionDialog
          open={showAmcConversion}
          onOpenChange={setShowAmcConversion}
          warrantyId={warranty.id}
          warrantyDetails={{
            id: warranty.id,
            customerId: warranty.customerId,
            customerName: warranty.customerName || 'Unknown Customer',
            bslNo: warranty.bslNo,
            numberOfMachines: warranty.numberOfMachines,
            executiveId: warranty.executiveId,
            contactPersonId: warranty.contactPersonId,
          }}
          onSuccess={onConversionSuccess}
        />
      </>
    );
  }

  if (variant === 'inline') {
    return (
      <>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size={size} disabled={isValidating}>
              <Shield className="w-4 h-4 mr-2" />
              To AMC
              <ChevronDown className="w-4 h-4 ml-2" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={handleAmcConversion}>
              <Shield className="w-4 h-4 mr-2" />
              Convert to AMC
            </DropdownMenuItem>
            <DropdownMenuItem onClick={handleOutWarrantyConversion}>
              <ShieldCheck className="w-4 h-4 mr-2" />
              Convert to Out-Warranty
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        <WarrantyToAmcConversionDialog
          open={showAmcConversion}
          onOpenChange={setShowAmcConversion}
          warrantyId={warranty.id}
          warrantyDetails={{
            id: warranty.id,
            customerId: warranty.customerId,
            customerName: warranty.customerName || 'Unknown Customer',
            bslNo: warranty.bslNo,
            numberOfMachines: warranty.numberOfMachines,
            executiveId: warranty.executiveId,
            contactPersonId: warranty.contactPersonId,
          }}
          onSuccess={onConversionSuccess}
        />

        <WarrantyToOutWarrantyConversionDialog
          open={showOutWarrantyConversion}
          onOpenChange={setShowOutWarrantyConversion}
          warrantyId={warranty.id}
          warrantyDetails={{
            id: warranty.id,
            customerId: warranty.customerId,
            customerName: warranty.customerName || 'Unknown Customer',
            bslNo: warranty.bslNo,
            numberOfMachines: warranty.numberOfMachines,
            executiveId: warranty.executiveId,
            contactPersonId: warranty.contactPersonId,
          }}
          onSuccess={onConversionSuccess}
        />
      </>
    );
  }

  // Default dropdown variant
  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size={size} disabled={isValidating}>
            <ArrowRightLeft className="w-4 h-4 mr-2" />
            Convert
            <ChevronDown className="w-4 h-4 ml-2" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem onClick={handleAmcConversion}>
            <Shield className="w-4 h-4 mr-2" />
            Convert to AMC
          </DropdownMenuItem>
          <DropdownMenuItem onClick={handleOutWarrantyConversion}>
            <ShieldCheck className="w-4 h-4 mr-2" />
            Convert to Out-Warranty
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <WarrantyToAmcConversionDialog
        open={showAmcConversion}
        onOpenChange={setShowAmcConversion}
        warrantyId={warranty.id}
        warrantyDetails={{
          id: warranty.id,
          customerId: warranty.customerId,
          customerName: warranty.customerName || 'Unknown Customer',
          bslNo: warranty.bslNo,
          numberOfMachines: warranty.numberOfMachines,
          executiveId: warranty.executiveId,
          contactPersonId: warranty.contactPersonId,
        }}
        onSuccess={onConversionSuccess}
      />

      <WarrantyToOutWarrantyConversionDialog
        open={showOutWarrantyConversion}
        onOpenChange={setShowOutWarrantyConversion}
        warrantyId={warranty.id}
        warrantyDetails={{
          id: warranty.id,
          customerId: warranty.customerId,
          customerName: warranty.customerName || 'Unknown Customer',
          bslNo: warranty.bslNo,
          numberOfMachines: warranty.numberOfMachines,
          executiveId: warranty.executiveId,
          contactPersonId: warranty.contactPersonId,
        }}
        onSuccess={onConversionSuccess}
      />
    </>
  );
}
