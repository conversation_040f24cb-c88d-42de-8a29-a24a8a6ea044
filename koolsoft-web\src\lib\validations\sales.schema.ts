import { z } from 'zod';

/**
 * Sales Lead Status Enum
 */
export const salesLeadStatusSchema = z.enum([
  'NEW',
  'CONTACTED',
  'QUALIFIED',
  'PROPOSAL',
  'NEGOTIATION',
  'CLOSED_WON',
  'CLOSED_LOST',
  'ON_HOLD'
], {
  errorMap: () => ({ message: 'Invalid sales lead status' })
});

/**
 * Sales Opportunity Status Enum
 */
export const salesOpportunityStatusSchema = z.enum([
  'OPEN',
  'QUALIFIED',
  'PROPOSAL',
  'NEGOTIATION',
  'CLOSED_WON',
  'CLOSED_LOST',
  'ON_HOLD'
], {
  errorMap: () => ({ message: 'Invalid sales opportunity status' })
});

/**
 * Sales Prospect Status Enum
 */
export const salesProspectStatusSchema = z.enum([
  'ACTIVE',
  'QUALIFIED',
  'CONVERTED',
  'LOST',
  'INACTIVE'
], {
  errorMap: () => ({ message: 'Invalid sales prospect status' })
});

/**
 * Sales Order Status Enum
 */
export const salesOrderStatusSchema = z.enum([
  'PENDING',
  'CONFIRMED',
  'IN_PROGRESS',
  'SHIPPED',
  'DELIVERED',
  'COMPLETED',
  'CANCELLED'
], {
  errorMap: () => ({ message: 'Invalid sales order status' })
});

/**
 * Base Sales Lead Schema
 */
export const salesLeadBaseSchema = z.object({
  customerId: z.string().uuid({ message: 'Valid customer ID is required' }),
  executiveId: z.string().uuid({ message: 'Valid executive ID is required' }),
  leadDate: z.coerce.date({ message: 'Valid lead date is required' }),
  contactPerson: z.string().max(100).optional(),
  contactPhone: z.string().max(20).optional(),
  status: salesLeadStatusSchema.default('NEW'),
  prospectPercentage: z.number().int().min(0).max(100).optional(),
  followUpDate: z.coerce.date().optional(),
  nextVisitDate: z.coerce.date().optional(),
  remarks: z.string().max(1000).optional(),
});

/**
 * Sales Lead Creation Schema
 */
export const createSalesLeadSchema = salesLeadBaseSchema.refine(
  (data) => {
    // Follow-up date should be after lead date if provided
    if (data.leadDate && data.followUpDate) {
      return data.followUpDate >= data.leadDate;
    }
    return true;
  },
  {
    message: 'Follow-up date must be after or equal to lead date',
    path: ['followUpDate'],
  }
).refine(
  (data) => {
    // Next visit date should be after lead date if provided
    if (data.leadDate && data.nextVisitDate) {
      return data.nextVisitDate >= data.leadDate;
    }
    return true;
  },
  {
    message: 'Next visit date must be after or equal to lead date',
    path: ['nextVisitDate'],
  }
);

/**
 * Sales Lead Update Schema
 */
export const updateSalesLeadSchema = salesLeadBaseSchema
  .partial()
  .extend({
    id: z.string().uuid(),
  })
  .refine(
    (data) => {
      // Follow-up date should be after lead date if both provided
      if (data.leadDate && data.followUpDate) {
        return data.followUpDate >= data.leadDate;
      }
      return true;
    },
    {
      message: 'Follow-up date must be after or equal to lead date',
      path: ['followUpDate'],
    }
  )
  .refine(
    (data) => {
      // Next visit date should be after lead date if both provided
      if (data.leadDate && data.nextVisitDate) {
        return data.nextVisitDate >= data.leadDate;
      }
      return true;
    },
    {
      message: 'Next visit date must be after or equal to lead date',
      path: ['nextVisitDate'],
    }
  );

/**
 * Base Sales Opportunity Schema
 */
export const salesOpportunityBaseSchema = z.object({
  customerId: z.string().uuid({ message: 'Valid customer ID is required' }),
  executiveId: z.string().uuid({ message: 'Valid executive ID is required' }),
  opportunityDate: z.coerce.date({ message: 'Valid opportunity date is required' }),
  contactPerson: z.string().max(100).optional(),
  contactPhone: z.string().max(20).optional(),
  status: salesOpportunityStatusSchema.default('OPEN'),
  prospectPercentage: z.number().int().min(0).max(100).optional(),
  followUpDate: z.coerce.date().optional(),
  nextVisitDate: z.coerce.date().optional(),
  remarks: z.string().max(1000).optional(),
});

/**
 * Sales Opportunity Creation Schema
 */
export const createSalesOpportunitySchema = salesOpportunityBaseSchema.refine(
  (data) => {
    // Follow-up date should be after opportunity date if provided
    if (data.opportunityDate && data.followUpDate) {
      return data.followUpDate >= data.opportunityDate;
    }
    return true;
  },
  {
    message: 'Follow-up date must be after or equal to opportunity date',
    path: ['followUpDate'],
  }
).refine(
  (data) => {
    // Next visit date should be after opportunity date if provided
    if (data.opportunityDate && data.nextVisitDate) {
      return data.nextVisitDate >= data.opportunityDate;
    }
    return true;
  },
  {
    message: 'Next visit date must be after or equal to opportunity date',
    path: ['nextVisitDate'],
  }
);

/**
 * Sales Opportunity Update Schema
 */
export const updateSalesOpportunitySchema = salesOpportunityBaseSchema
  .partial()
  .extend({
    id: z.string().uuid(),
  })
  .refine(
    (data) => {
      // Follow-up date should be after opportunity date if both provided
      if (data.opportunityDate && data.followUpDate) {
        return data.followUpDate >= data.opportunityDate;
      }
      return true;
    },
    {
      message: 'Follow-up date must be after or equal to opportunity date',
      path: ['followUpDate'],
    }
  )
  .refine(
    (data) => {
      // Next visit date should be after opportunity date if both provided
      if (data.opportunityDate && data.nextVisitDate) {
        return data.nextVisitDate >= data.opportunityDate;
      }
      return true;
    },
    {
      message: 'Next visit date must be after or equal to opportunity date',
      path: ['nextVisitDate'],
    }
  );

/**
 * Base Sales Prospect Schema
 */
export const salesProspectBaseSchema = z.object({
  customerId: z.string().uuid({ message: 'Valid customer ID is required' }),
  executiveId: z.string().uuid({ message: 'Valid executive ID is required' }),
  prospectDate: z.coerce.date({ message: 'Valid prospect date is required' }),
  contactPerson: z.string().max(100).optional(),
  contactPhone: z.string().max(20).optional(),
  status: salesProspectStatusSchema.default('ACTIVE'),
  prospectPercentage: z.number().int().min(0).max(100).optional(),
  followUpDate: z.coerce.date().optional(),
  nextVisitDate: z.coerce.date().optional(),
  lostReason: z.string().max(500).optional(),
  remarks: z.string().max(1000).optional(),
});

/**
 * Sales Prospect Creation Schema
 */
export const createSalesProspectSchema = salesProspectBaseSchema.refine(
  (data) => {
    // Follow-up date should be after prospect date if provided
    if (data.prospectDate && data.followUpDate) {
      return data.followUpDate >= data.prospectDate;
    }
    return true;
  },
  {
    message: 'Follow-up date must be after or equal to prospect date',
    path: ['followUpDate'],
  }
).refine(
  (data) => {
    // Next visit date should be after prospect date if provided
    if (data.prospectDate && data.nextVisitDate) {
      return data.nextVisitDate >= data.prospectDate;
    }
    return true;
  },
  {
    message: 'Next visit date must be after or equal to prospect date',
    path: ['nextVisitDate'],
  }
).refine(
  (data) => {
    // Lost reason is required when status is LOST
    if (data.status === 'LOST' && !data.lostReason) {
      return false;
    }
    return true;
  },
  {
    message: 'Lost reason is required when status is LOST',
    path: ['lostReason'],
  }
);

/**
 * Sales Prospect Update Schema
 */
export const updateSalesProspectSchema = salesProspectBaseSchema
  .partial()
  .extend({
    id: z.string().uuid(),
  })
  .refine(
    (data) => {
      // Follow-up date should be after prospect date if both provided
      if (data.prospectDate && data.followUpDate) {
        return data.followUpDate >= data.prospectDate;
      }
      return true;
    },
    {
      message: 'Follow-up date must be after or equal to prospect date',
      path: ['followUpDate'],
    }
  )
  .refine(
    (data) => {
      // Next visit date should be after prospect date if both provided
      if (data.prospectDate && data.nextVisitDate) {
        return data.nextVisitDate >= data.prospectDate;
      }
      return true;
    },
    {
      message: 'Next visit date must be after or equal to prospect date',
      path: ['nextVisitDate'],
    }
  )
  .refine(
    (data) => {
      // Lost reason is required when status is LOST
      if (data.status === 'LOST' && !data.lostReason) {
        return false;
      }
      return true;
    },
    {
      message: 'Lost reason is required when status is LOST',
      path: ['lostReason'],
    }
  );

/**
 * Base Sales Order Schema
 */
export const salesOrderBaseSchema = z.object({
  customerId: z.string().uuid({ message: 'Valid customer ID is required' }),
  executiveId: z.string().uuid({ message: 'Valid executive ID is required' }),
  orderDate: z.coerce.date({ message: 'Valid order date is required' }),
  contactPerson: z.string().max(100).optional(),
  contactPhone: z.string().max(20).optional(),
  status: salesOrderStatusSchema.default('PENDING'),
  deliveryDate: z.coerce.date().optional(),
  actualDeliveryDate: z.coerce.date().optional(),
  amount: z.number({ message: 'Valid amount is required' }).min(0, { message: 'Amount must be non-negative' }),
  remarks: z.string().max(1000).optional(),
});

/**
 * Sales Order Creation Schema
 */
export const createSalesOrderSchema = salesOrderBaseSchema.refine(
  (data) => {
    // Delivery date should be after order date if provided
    if (data.orderDate && data.deliveryDate) {
      return data.deliveryDate >= data.orderDate;
    }
    return true;
  },
  {
    message: 'Delivery date must be after or equal to order date',
    path: ['deliveryDate'],
  }
).refine(
  (data) => {
    // Actual delivery date should be after order date if provided
    if (data.orderDate && data.actualDeliveryDate) {
      return data.actualDeliveryDate >= data.orderDate;
    }
    return true;
  },
  {
    message: 'Actual delivery date must be after or equal to order date',
    path: ['actualDeliveryDate'],
  }
);

/**
 * Sales Order Update Schema
 */
export const updateSalesOrderSchema = salesOrderBaseSchema
  .partial()
  .extend({
    id: z.string().uuid(),
  })
  .refine(
    (data) => {
      // Delivery date should be after order date if both provided
      if (data.orderDate && data.deliveryDate) {
        return data.deliveryDate >= data.orderDate;
      }
      return true;
    },
    {
      message: 'Delivery date must be after or equal to order date',
      path: ['deliveryDate'],
    }
  )
  .refine(
    (data) => {
      // Actual delivery date should be after order date if both provided
      if (data.orderDate && data.actualDeliveryDate) {
        return data.actualDeliveryDate >= data.orderDate;
      }
      return true;
    },
    {
      message: 'Actual delivery date must be after or equal to order date',
      path: ['actualDeliveryDate'],
    }
  );

/**
 * Sales Filter Schema for API queries
 */
export const salesFilterSchema = z.object({
  customerId: z.string().uuid().optional(),
  executiveId: z.string().uuid().optional(),
  status: z.string().optional(),
  startDate: z.coerce.date().optional(),
  endDate: z.coerce.date().optional(),
  search: z.string().optional(),
  skip: z.coerce.number().min(0).default(0),
  take: z.coerce.number().min(1).max(100).default(10),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
}).refine(
  (data) => {
    // End date should be after start date if both provided
    if (data.startDate && data.endDate) {
      return data.endDate >= data.startDate;
    }
    return true;
  },
  {
    message: 'End date must be after or equal to start date',
    path: ['endDate'],
  }
);
