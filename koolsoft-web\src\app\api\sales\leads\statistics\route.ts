import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { getSalesLeadRepository } from '@/lib/repositories';
import { z } from 'zod';

/**
 * Statistics filter schema
 */
const statisticsFilterSchema = z.object({
  customerId: z.string().uuid().optional(),
  executiveId: z.string().uuid().optional(),
  startDate: z.coerce.date().optional(),
  endDate: z.coerce.date().optional(),
}).refine(
  (data) => {
    // End date should be after start date if both provided
    if (data.startDate && data.endDate) {
      return data.endDate >= data.startDate;
    }
    return true;
  },
  {
    message: 'End date must be after or equal to start date',
    path: ['endDate'],
  }
);

/**
 * GET /api/sales/leads/statistics
 * Get sales leads statistics
 */
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  async (request: NextRequest) => {
    try {
      const { searchParams } = new URL(request.url);
      
      // Parse and validate query parameters
      const filters = {
        customerId: searchParams.get('customerId') || undefined,
        executiveId: searchParams.get('executiveId') || undefined,
        startDate: searchParams.get('startDate') || undefined,
        endDate: searchParams.get('endDate') || undefined,
      };

      const validatedFilters = statisticsFilterSchema.parse(filters);

      const salesLeadRepository = getSalesLeadRepository();
      const statistics = await salesLeadRepository.getStatistics(validatedFilters);

      return NextResponse.json({
        success: true,
        data: statistics,
      });
    } catch (error) {
      console.error('Error fetching sales leads statistics:', error);
      
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid request parameters',
            details: error.errors,
          },
          { status: 400 }
        );
      }
      
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to fetch sales leads statistics',
        },
        { status: 500 }
      );
    }
  }
);
