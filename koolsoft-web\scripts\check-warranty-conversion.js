const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkWarrantyConversion() {
  try {
    console.log('🔍 Investigating warranty conversion for ID: 9de7b408-38cf-43ea-ace7-7e46d787c48e\n');

    // Check if the warranty record exists
    const warranty = await prisma.warranties.findUnique({
      where: { id: '9de7b408-38cf-43ea-ace7-7e46d787c48e' },
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            city: true,
            phone: true,
          }
        },
        users: {
          select: {
            id: true,
            name: true,
            email: true,
          }
        },
        machines: {
          include: {
            components: true,
            model: {
              include: {
                product: {
                  include: {
                    brand: true,
                  }
                }
              }
            },
          }
        }
      }
    });
    
    if (!warranty) {
      console.log('❌ Warranty record not found with ID: 9de7b408-38cf-43ea-ace7-7e46d787c48e');
      
      // Search for similar records
      const similarWarranties = await prisma.warranties.findMany({
        where: {
          customer: {
            name: {
              contains: 'St.mulk Orthodox Church',
              mode: 'insensitive'
            }
          }
        },
        include: {
          customer: {
            select: {
              id: true,
              name: true,
              city: true,
            }
          }
        },
        take: 5
      });
      
      console.log(`\n🔍 Found ${similarWarranties.length} similar warranties for this customer:`);
      similarWarranties.forEach((w, index) => {
        console.log(`${index + 1}. ID: ${w.id}`);
        console.log(`   Customer: ${w.customer?.name}`);
        console.log(`   Status: ${w.status}`);
        console.log(`   Start Date: ${w.startDate}`);
        console.log(`   End Date: ${w.endDate}`);
        console.log('   ---');
      });
      
      return;
    }
    
    console.log('✅ Warranty record found:');
    console.log(`   ID: ${warranty.id}`);
    console.log(`   Customer: ${warranty.customer?.name}`);
    console.log(`   Executive ID: ${warranty.executiveId}`);
    console.log(`   Executive: ${warranty.users?.name || 'Not found'}`);
    console.log(`   Status: ${warranty.status}`);
    console.log(`   Install Date: ${warranty.installDate}`);
    console.log(`   Warranty Date: ${warranty.warrantyDate}`);
    console.log(`   Warning Date: ${warranty.warningDate}`);
    console.log(`   BSL Amount: ${warranty.bslAmount}`);
    console.log(`   Machines: ${warranty.machines?.length || 0}`);
    
    // Check if already converted by looking at the warranty's amcId field
    let existingAMC = null;
    if (warranty.amcId) {
      existingAMC = await prisma.amc_contracts.findUnique({
        where: { id: warranty.amcId }
      });
    }

    // Also check if there's an out-warranty record that references this warranty
    const existingOutWarranty = await prisma.out_warranties.findFirst({
      where: {
        inWarrantySourceId: warranty.id
      }
    });
    
    if (existingAMC) {
      console.log('\n⚠️  This warranty has already been converted to AMC:');
      console.log(`   AMC ID: ${existingAMC.id}`);
      console.log(`   AMC Status: ${existingAMC.status}`);
      console.log(`   AMC Start Date: ${existingAMC.startDate}`);
      console.log(`   AMC End Date: ${existingAMC.endDate}`);
    } else if (existingOutWarranty) {
      console.log('\n⚠️  This warranty has been converted to Out-Warranty:');
      console.log(`   Out-Warranty ID: ${existingOutWarranty.id}`);
      console.log(`   Out-Warranty Status: ${existingOutWarranty.isActive ? 'Active' : 'Inactive'}`);
      console.log(`   Out-Warranty Start Date: ${existingOutWarranty.startDate}`);
      console.log(`   Out-Warranty End Date: ${existingOutWarranty.endDate}`);
    } else {
      console.log('\n✅ No existing conversion found - ready for conversion');
    }
    
    // Check conversion eligibility
    console.log('\n📋 Conversion Eligibility Check:');
    console.log(`   ✅ Warranty exists: ${!!warranty}`);
    console.log(`   ✅ Has customer: ${!!warranty.customer}`);
    console.log(`   ✅ Has executive ID: ${!!warranty.executiveId}`);
    console.log(`   ✅ Has machines: ${warranty.machines?.length > 0}`);
    console.log(`   ✅ Not already converted to AMC: ${!existingAMC}`);
    console.log(`   ✅ Not already converted to Out-Warranty: ${!existingOutWarranty}`);

    const isEligible = warranty && warranty.customer && warranty.executiveId &&
                      warranty.machines?.length > 0 && !existingAMC && !existingOutWarranty;
    
    console.log(`\n🎯 Overall Eligibility: ${isEligible ? '✅ ELIGIBLE' : '❌ NOT ELIGIBLE'}`);
    
  } catch (error) {
    console.error('❌ Error checking warranty conversion:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkWarrantyConversion();
