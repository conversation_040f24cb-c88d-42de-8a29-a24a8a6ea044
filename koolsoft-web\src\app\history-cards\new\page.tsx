'use client';

import { useRouter } from 'next/navigation';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { HistoryCardForm } from '@/components/history-cards';
import { 
  ArrowLeft, 
  Plus,
} from 'lucide-react';

interface HistoryCard {
  id: string;
  cardNo?: number;
  source?: 'AMC' | 'INW' | 'OTW';
  customerId: string;
  amcId?: string;
  inWarrantyId?: string;
  outWarrantyId?: string;
  toCardNo?: number;
  originalId?: number;
  createdAt: string;
  updatedAt: string;
  customer?: {
    id: string;
    name: string;
    address?: string;
    city?: string;
    state?: string;
    phone?: string;
    email?: string;
  };
  sections?: {
    id: string;
    sectionCode: string;
    content: string;
    createdAt: string;
    updatedAt: string;
  }[];
}

export default function NewHistoryCardPage() {
  const router = useRouter();
  const { toast } = useToast();

  const handleSuccess = (newCard: HistoryCard) => {
    toast({
      title: 'Success',
      description: 'History card created successfully.',
    });
    
    // Navigate to the new card's detail page
    router.push(`/history-cards/${newCard.id}`);
  };

  const handleCancel = () => {
    // Navigate back to the list
    router.push('/history-cards');
  };

  const handleBack = () => {
    router.push('/history-cards');
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <Card>
        <CardHeader className="pb-3 flex flex-row items-center justify-between bg-primary text-white">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <Plus className="h-5 w-5" />
              <span>Create New History Card</span>
            </CardTitle>
            <p className="text-gray-100 mt-1">
              Add a new history card to track customer service history
            </p>
          </div>
          <div className="flex space-x-2">
            <Button variant="secondary" onClick={handleBack}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to List
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* Create Form */}
      <HistoryCardForm
        onSuccess={handleSuccess}
        onCancel={handleCancel}
      />
    </div>
  );
}
