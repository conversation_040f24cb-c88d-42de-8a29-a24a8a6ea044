import { z } from 'zod';

/**
 * Conversion Types
 */
export const conversionTypeSchema = z.enum([
  'WARRANTY_TO_AMC',
  'AMC_TO_OUT_WARRANTY',
  'WARRANTY_TO_OUT_WARRANTY'
], {
  errorMap: () => ({ message: 'Invalid conversion type' })
});

/**
 * Base conversion request schema
 */
export const baseConversionSchema = z.object({
  sourceId: z.string().uuid({ message: 'Valid source ID is required' }),
  conversionType: conversionTypeSchema,
  reason: z.string().min(1, { message: 'Conversion reason is required' }).max(500),
  effectiveDate: z.coerce.date({ message: 'Valid effective date is required' }),
  notes: z.string().max(1000).optional(),
});

/**
 * Warranty to AMC conversion schema
 */
export const warrantyToAmcConversionSchema = baseConversionSchema.extend({
  conversionType: z.literal('WARRANTY_TO_AMC'),
  amcData: z.object({
    startDate: z.coerce.date({ message: 'Valid AMC start date is required' }),
    endDate: z.coerce.date({ message: 'Valid AMC end date is required' }),
    amount: z.number({
      required_error: 'AMC amount is required',
      invalid_type_error: 'AMC amount must be a number'
    }).positive({ message: 'AMC amount must be greater than 0' }),
    numberOfServices: z.number({
      invalid_type_error: 'Number of services must be a number'
    }).int().positive().default(4),
    natureOfService: z.string().min(1, { message: 'Nature of service is required' }),
    executiveId: z.string().uuid().optional(),
    contactPersonId: z.string().uuid().optional(),
  }).refine(
    (data) => data.endDate > data.startDate,
    {
      message: 'AMC end date must be after start date',
      path: ['endDate'],
    }
  ),
}).refine(
  (data) => data.amcData.startDate >= data.effectiveDate,
  {
    message: 'AMC start date must be on or after effective date',
    path: ['amcData', 'startDate'],
  }
);

/**
 * AMC to Out-of-Warranty conversion schema
 */
export const amcToOutWarrantyConversionSchema = baseConversionSchema.extend({
  conversionType: z.literal('AMC_TO_OUT_WARRANTY'),
  outWarrantyData: z.object({
    startDate: z.coerce.date({ message: 'Valid out-warranty start date is required' }),
    endDate: z.coerce.date({ message: 'Valid out-warranty end date is required' }),
    executiveId: z.string().uuid().optional(),
    contactPersonId: z.string().uuid().optional(),
    technicianId: z.string().uuid().optional(),
  }).refine(
    (data) => data.endDate > data.startDate,
    {
      message: 'Out-warranty end date must be after start date',
      path: ['endDate'],
    }
  ),
}).refine(
  (data) => data.outWarrantyData.startDate >= data.effectiveDate,
  {
    message: 'Out-warranty start date must be on or after effective date',
    path: ['outWarrantyData', 'startDate'],
  }
);

/**
 * Warranty to Out-of-Warranty conversion schema
 */
export const warrantyToOutWarrantyConversionSchema = baseConversionSchema.extend({
  conversionType: z.literal('WARRANTY_TO_OUT_WARRANTY'),
  outWarrantyData: z.object({
    startDate: z.coerce.date({ message: 'Valid out-warranty start date is required' }),
    endDate: z.coerce.date({ message: 'Valid out-warranty end date is required' }),
    executiveId: z.string().uuid().optional(),
    contactPersonId: z.string().uuid().optional(),
    technicianId: z.string().uuid().optional(),
  }).refine(
    (data) => data.endDate > data.startDate,
    {
      message: 'Out-warranty end date must be after start date',
      path: ['endDate'],
    }
  ),
}).refine(
  (data) => data.outWarrantyData.startDate >= data.effectiveDate,
  {
    message: 'Out-warranty start date must be on or after effective date',
    path: ['outWarrantyData', 'startDate'],
  }
);

/**
 * Union schema for all conversion types
 */
export const conversionRequestSchema = z.union([
  warrantyToAmcConversionSchema,
  amcToOutWarrantyConversionSchema,
  warrantyToOutWarrantyConversionSchema,
]);

/**
 * Conversion response schema
 */
export const conversionResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  sourceId: z.string().uuid(),
  targetId: z.string().uuid().optional(),
  historyCardId: z.string().uuid().optional(),
  conversionType: conversionTypeSchema,
  effectiveDate: z.date(),
});

/**
 * Conversion history filter schema
 */
export const conversionHistoryFilterSchema = z.object({
  customerId: z.string().uuid().optional(),
  conversionType: conversionTypeSchema.optional(),
  dateFrom: z.coerce.date().optional(),
  dateTo: z.coerce.date().optional(),
  skip: z.number().int().min(0).default(0),
  take: z.number().int().min(1).max(100).default(10),
});

/**
 * Conversion report filter schema
 */
export const conversionReportFilterSchema = z.object({
  dateFrom: z.coerce.date().optional(),
  dateTo: z.coerce.date().optional(),
  conversionType: conversionTypeSchema.optional(),
  customerId: z.string().uuid().optional(),
  status: z.enum(['SUCCESS', 'FAILED', 'PENDING']).optional(),
  userId: z.string().uuid().optional(),
  amountMin: z.number().positive().optional(),
  amountMax: z.number().positive().optional(),
  page: z.number().int().positive().default(1),
  limit: z.number().int().positive().max(100).default(20),
  orderBy: z.enum(['createdAt', 'effectiveDate', 'amount']).default('createdAt'),
  orderDirection: z.enum(['asc', 'desc']).default('desc'),
});

/**
 * Conversion statistics response schema
 */
export const conversionStatisticsSchema = z.object({
  totalConversions: z.number().int().nonnegative(),
  conversionsByType: z.array(z.object({
    type: conversionTypeSchema,
    count: z.number().int().nonnegative(),
    percentage: z.number().min(0).max(100),
  })),
  conversionsByStatus: z.array(z.object({
    status: z.enum(['SUCCESS', 'FAILED', 'PENDING']),
    count: z.number().int().nonnegative(),
    percentage: z.number().min(0).max(100),
  })),
  totalRevenue: z.number().nonnegative(),
  averageConversionValue: z.number().nonnegative(),
  successRate: z.number().min(0).max(100),
  periodComparison: z.object({
    previousPeriodTotal: z.number().int().nonnegative(),
    growthPercentage: z.number(),
  }).optional(),
});

/**
 * Conversion trends response schema
 */
export const conversionTrendsSchema = z.object({
  period: z.enum(['daily', 'weekly', 'monthly', 'quarterly']),
  data: z.array(z.object({
    date: z.string(),
    totalConversions: z.number().int().nonnegative(),
    warrantyToAmc: z.number().int().nonnegative(),
    amcToOutWarranty: z.number().int().nonnegative(),
    warrantyToOutWarranty: z.number().int().nonnegative(),
    totalRevenue: z.number().nonnegative(),
    successRate: z.number().min(0).max(100),
  })),
});

/**
 * Conversion export schema
 */
export const conversionExportSchema = z.object({
  format: z.enum(['CSV', 'EXCEL', 'PDF']).default('CSV'),
  filters: conversionReportFilterSchema.optional(),
  includeDetails: z.boolean().default(true),
  includeStatistics: z.boolean().default(false),
});

/**
 * Type exports
 */
export type ConversionType = z.infer<typeof conversionTypeSchema>;
export type BaseConversionRequest = z.infer<typeof baseConversionSchema>;
export type WarrantyToAmcConversion = z.infer<typeof warrantyToAmcConversionSchema>;
export type AmcToOutWarrantyConversion = z.infer<typeof amcToOutWarrantyConversionSchema>;
export type WarrantyToOutWarrantyConversion = z.infer<typeof warrantyToOutWarrantyConversionSchema>;
export type ConversionRequest = z.infer<typeof conversionRequestSchema>;
export type ConversionResponse = z.infer<typeof conversionResponseSchema>;
export type ConversionHistoryFilter = z.infer<typeof conversionHistoryFilterSchema>;
export type ConversionReportFilter = z.infer<typeof conversionReportFilterSchema>;
export type ConversionStatistics = z.infer<typeof conversionStatisticsSchema>;
export type ConversionTrends = z.infer<typeof conversionTrendsSchema>;
export type ConversionExport = z.infer<typeof conversionExportSchema>;

/**
 * Bulk conversion schema (defined after conversionRequestSchema to avoid circular dependency)
 */
export const bulkConversionSchema = z.object({
  conversions: z.array(conversionRequestSchema).min(1, { message: 'At least one conversion is required' }).max(50, { message: 'Maximum 50 conversions allowed per batch' }),
  validateOnly: z.boolean().default(false),
});

export type BulkConversionRequest = z.infer<typeof bulkConversionSchema>;
