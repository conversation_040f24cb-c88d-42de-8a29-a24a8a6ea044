'use client';

import React from 'react';
import { DashboardLayout } from '@/components/layout';
import { SalesPipelineBoard } from '@/components/sales/sales-pipeline-board';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Plus, Filter, Download } from 'lucide-react';
import Link from 'next/link';

/**
 * Sales Pipeline Page
 * 
 * This page provides a kanban board interface for managing the sales pipeline.
 * It displays sales leads, opportunities, prospects, and orders in a visual
 * drag-and-drop interface organized by status.
 */
export default function SalesPipelinePage() {
  const breadcrumbs = [
    { label: 'Sales', href: '/sales' },
    { label: 'Pipeline', href: '/sales/pipeline' },
  ];

  return (
    <DashboardLayout
      title="Sales Pipeline"
      breadcrumbs={breadcrumbs}
      requireAuth={true}
      allowedRoles={['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER']}
    >
      <div className="space-y-6">
        {/* Action Bar */}
        <Card>
          <CardHeader className="bg-primary text-primary-foreground">
            <div className="flex items-center justify-between">
              <div>
                <CardDescription className="text-primary-foreground/80">
                  Manage your sales pipeline with drag-and-drop kanban board
                </CardDescription>
              </div>
              <div className="flex items-center space-x-2">
                <Button asChild variant="secondary" size="sm">
                  <Link href="/leads/new">
                    <Plus className="mr-2 h-4 w-4" />
                    New Lead
                  </Link>
                </Button>
                <Button variant="secondary" size="sm">
                  <Filter className="mr-2 h-4 w-4" />
                  Filter
                </Button>
                <Button variant="secondary" size="sm">
                  <Download className="mr-2 h-4 w-4" />
                  Export
                </Button>
              </div>
            </div>
          </CardHeader>
        </Card>

        {/* Pipeline Board */}
        <SalesPipelineBoard />
      </div>
    </DashboardLayout>
  );
}
