'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/components/ui/use-toast';
import { 
  MoreVertical, 
  Download, 
  Trash2, 
  Edit, 
  FileText,
  Loader2,
  AlertTriangle,
} from 'lucide-react';

interface HistoryCardActionsProps {
  selectedCardIds: string[];
  onRefresh?: () => void;
  onClearSelection?: () => void;
}

export function HistoryCardActions({
  selectedCardIds,
  onRefresh,
  onClearSelection,
}: HistoryCardActionsProps) {
  const { toast } = useToast();
  
  const [isExporting, setIsExporting] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showBulkUpdateDialog, setShowBulkUpdateDialog] = useState(false);
  const [exportFormat, setExportFormat] = useState<'csv' | 'excel'>('csv');
  const [bulkUpdateSource, setBulkUpdateSource] = useState<string>('');

  const handleExport = async (format: 'csv' | 'excel' = exportFormat) => {
    try {
      setIsExporting(true);
      
      // For bulk export, we'll export all selected cards
      const params = new URLSearchParams({
        format,
        includeDetails: 'true',
      });
      
      const response = await fetch(`/api/history-cards/export?${params}`, {
        credentials: 'include',
      });
      
      if (!response.ok) {
        throw new Error('Failed to export history cards');
      }
      
      if (format === 'csv') {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = `history-cards-${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
      } else {
        const data = await response.json();
        if (data.success) {
          // For Excel, we would typically use a library like xlsx
          // For now, we'll download as JSON and let the user know
          const blob = new Blob([JSON.stringify(data.data, null, 2)], {
            type: 'application/json',
          });
          const url = window.URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.style.display = 'none';
          a.href = url;
          a.download = data.filename || 'history-cards.json';
          document.body.appendChild(a);
          a.click();
          window.URL.revokeObjectURL(url);
        }
      }
      
      toast({
        title: 'Success',
        description: `History cards exported successfully as ${format.toUpperCase()}.`,
      });
    } catch (error) {
      console.error('Error exporting history cards:', error);
      toast({
        title: 'Error',
        description: 'Failed to export history cards. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsExporting(false);
    }
  };

  const handleBulkDelete = async () => {
    try {
      setIsDeleting(true);
      
      const response = await fetch('/api/history-cards/bulk', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          action: 'delete',
          ids: selectedCardIds,
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to delete history cards');
      }
      
      const data = await response.json();
      
      if (data.success) {
        toast({
          title: 'Success',
          description: `Successfully deleted ${data.data.deletedCount} history cards.`,
        });
        
        setShowDeleteDialog(false);
        if (onClearSelection) onClearSelection();
        if (onRefresh) onRefresh();
      } else {
        throw new Error(data.error || 'Failed to delete history cards');
      }
    } catch (error) {
      console.error('Error deleting history cards:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete history cards. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsDeleting(false);
    }
  };

  const handleBulkUpdate = async () => {
    if (!bulkUpdateSource) {
      toast({
        title: 'Error',
        description: 'Please select a source to update.',
        variant: 'destructive',
      });
      return;
    }
    
    try {
      const response = await fetch('/api/history-cards/bulk', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          action: 'update',
          ids: selectedCardIds,
          data: {
            source: bulkUpdateSource,
          },
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to update history cards');
      }
      
      const data = await response.json();
      
      if (data.success) {
        toast({
          title: 'Success',
          description: `Successfully updated ${data.data.updatedCount} history cards.`,
        });
        
        setShowBulkUpdateDialog(false);
        setBulkUpdateSource('');
        if (onClearSelection) onClearSelection();
        if (onRefresh) onRefresh();
      } else {
        throw new Error(data.error || 'Failed to update history cards');
      }
    } catch (error) {
      console.error('Error updating history cards:', error);
      toast({
        title: 'Error',
        description: 'Failed to update history cards. Please try again.',
        variant: 'destructive',
      });
    }
  };

  if (selectedCardIds.length === 0) {
    return null;
  }

  return (
    <>
      <div className="flex items-center gap-2">
        <span className="text-sm text-gray-600">
          {selectedCardIds.length} selected
        </span>
        
        <Button
          variant="outline"
          size="sm"
          onClick={() => handleExport('csv')}
          disabled={isExporting}
        >
          {isExporting ? (
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          ) : (
            <Download className="h-4 w-4 mr-2" />
          )}
          Export CSV
        </Button>
        
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm">
              <MoreVertical className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => handleExport('excel')}>
              <FileText className="h-4 w-4 mr-2" />
              Export Excel
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => setShowBulkUpdateDialog(true)}>
              <Edit className="h-4 w-4 mr-2" />
              Bulk Update Source
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem 
              onClick={() => setShowDeleteDialog(true)}
              className="text-red-600"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Delete Selected
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-red-500" />
              <span>Confirm Deletion</span>
            </DialogTitle>
            <DialogDescription>
              Are you sure you want to delete {selectedCardIds.length} history card{selectedCardIds.length > 1 ? 's' : ''}? 
              This action cannot be undone and will also delete all associated history sections.
            </DialogDescription>
          </DialogHeader>
          
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              This will permanently delete the selected history cards and all their sections.
            </AlertDescription>
          </Alert>
          
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowDeleteDialog(false)}
              disabled={isDeleting}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleBulkDelete}
              disabled={isDeleting}
            >
              {isDeleting ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Trash2 className="h-4 w-4 mr-2" />
              )}
              Delete {selectedCardIds.length} Card{selectedCardIds.length > 1 ? 's' : ''}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Bulk Update Dialog */}
      <Dialog open={showBulkUpdateDialog} onOpenChange={setShowBulkUpdateDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Bulk Update Source</DialogTitle>
            <DialogDescription>
              Update the source for {selectedCardIds.length} selected history card{selectedCardIds.length > 1 ? 's' : ''}.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium">New Source</label>
              <Select value={bulkUpdateSource} onValueChange={setBulkUpdateSource}>
                <SelectTrigger>
                  <SelectValue placeholder="Select new source" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="AMC">AMC</SelectItem>
                  <SelectItem value="INW">In-Warranty</SelectItem>
                  <SelectItem value="OTW">Out-of-Warranty</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setShowBulkUpdateDialog(false);
                setBulkUpdateSource('');
              }}
            >
              Cancel
            </Button>
            <Button onClick={handleBulkUpdate} disabled={!bulkUpdateSource}>
              <Edit className="h-4 w-4 mr-2" />
              Update {selectedCardIds.length} Card{selectedCardIds.length > 1 ? 's' : ''}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
