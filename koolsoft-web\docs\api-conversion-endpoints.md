# Conversion API Endpoints Documentation

## Overview

The Conversion API provides endpoints for converting data between different modules in the KoolSoft system. It supports conversions between Warranty, AMC (Annual Maintenance Contract), and Out-of-Warranty modules with proper validation, authentication, and database integrity.

## Authentication

All endpoints require authentication and role-based access control:
- **GET endpoints**: Accessible to ADMIN, MANAGER, EXECUTIVE, USER roles
- **POST/PUT endpoints**: Restricted to ADMIN, MANAGER roles only

## Base URL

```
http://localhost:3000/api/conversions
```

## Endpoints

### 1. Get Conversion Types

**GET** `/api/conversions/types`

Returns available conversion types and their metadata.

**Response:**
```json
{
  "conversionTypes": [
    {
      "type": "WARRANTY_TO_AMC",
      "name": "Warranty to AMC",
      "description": "Convert an in-warranty record to an Annual Maintenance Contract",
      "sourceModule": "Warranty",
      "targetModule": "AMC",
      "requiredFields": ["startDate", "endDate", "amount", "numberOfServices", "natureOfService"],
      "optionalFields": ["executiveId", "contactPersonId"]
    },
    {
      "type": "AMC_TO_OUT_WARRANTY",
      "name": "AMC to Out-of-Warranty",
      "description": "Convert an AMC contract to an out-of-warranty service record",
      "sourceModule": "AMC",
      "targetModule": "Out-of-Warranty",
      "requiredFields": ["startDate", "endDate"],
      "optionalFields": ["executiveId", "contactPersonId", "technicianId"]
    },
    {
      "type": "WARRANTY_TO_OUT_WARRANTY",
      "name": "Warranty to Out-of-Warranty",
      "description": "Convert an in-warranty record directly to an out-of-warranty service record",
      "sourceModule": "Warranty",
      "targetModule": "Out-of-Warranty",
      "requiredFields": ["startDate", "endDate"],
      "optionalFields": ["executiveId", "contactPersonId", "technicianId"]
    }
  ],
  "totalTypes": 3
}
```

### 2. Get Conversion History

**GET** `/api/conversions`

Retrieves conversion history with filtering and pagination.

**Query Parameters:**
- `customerId` (optional): Filter by customer UUID
- `conversionType` (optional): Filter by conversion type
- `dateFrom` (optional): Filter from date (ISO format)
- `dateTo` (optional): Filter to date (ISO format)
- `skip` (optional): Number of records to skip (default: 0)
- `take` (optional): Number of records to return (default: 10, max: 100)

**Response:**
```json
{
  "data": [
    {
      "id": "uuid",
      "customerId": "uuid",
      "cardNo": 2967,
      "source": "WARRANTY_TO_AMC",
      "amcId": "uuid",
      "inWarrantyId": "uuid",
      "outWarrantyId": null,
      "createdAt": "2025-01-15T10:30:00Z",
      "customer": {
        "id": "uuid",
        "name": "Customer Name"
      },
      "amcContract": {
        "id": "uuid",
        "amount": 50000,
        "startDate": "2025-01-15T00:00:00Z",
        "endDate": "2026-01-15T00:00:00Z",
        "status": "ACTIVE"
      },
      "inWarranty": {
        "id": "uuid",
        "bslNo": "BSL123456",
        "installDate": "2024-01-15T00:00:00Z",
        "warrantyDate": "2025-01-15T00:00:00Z"
      },
      "outWarranty": null
    }
  ],
  "pagination": {
    "total": 150,
    "skip": 0,
    "take": 10,
    "hasMore": true
  }
}
```

### 3. Validate Conversion

**GET** `/api/conversions/validate`

Validates if a conversion is possible for a given source record.

**Query Parameters:**
- `sourceId` (required): UUID of the source record
- `conversionType` (required): Type of conversion to validate

**POST** `/api/conversions/validate`

Alternative POST method for validation.

**Request Body:**
```json
{
  "sourceId": "550e8400-e29b-41d4-a716-************",
  "conversionType": "WARRANTY_TO_AMC"
}
```

**Response:**
```json
{
  "sourceId": "550e8400-e29b-41d4-a716-************",
  "conversionType": "WARRANTY_TO_AMC",
  "valid": false,
  "message": "Warranty not found"
}
```

### 4. Process Single Conversion

**POST** `/api/conversions`

Processes a single conversion request.

**Request Body Examples:**

#### Warranty to AMC Conversion:
```json
{
  "sourceId": "uuid",
  "conversionType": "WARRANTY_TO_AMC",
  "reason": "Customer requested AMC conversion",
  "effectiveDate": "2025-01-15T00:00:00Z",
  "notes": "Additional conversion notes",
  "amcData": {
    "startDate": "2025-01-15T00:00:00Z",
    "endDate": "2026-01-15T00:00:00Z",
    "amount": 50000,
    "numberOfServices": 4,
    "natureOfService": "Preventive Maintenance",
    "executiveId": "uuid",
    "contactPersonId": "uuid"
  }
}
```

#### AMC to Out-of-Warranty Conversion:
```json
{
  "sourceId": "uuid",
  "conversionType": "AMC_TO_OUT_WARRANTY",
  "reason": "AMC contract expired",
  "effectiveDate": "2025-01-15T00:00:00Z",
  "notes": "Converting to out-of-warranty service",
  "outWarrantyData": {
    "startDate": "2025-01-15T00:00:00Z",
    "endDate": "2026-01-15T00:00:00Z",
    "executiveId": "uuid",
    "contactPersonId": "uuid",
    "technicianId": "uuid"
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Warranty successfully converted to AMC",
  "sourceId": "uuid",
  "targetId": "uuid",
  "historyCardId": "uuid",
  "conversionType": "WARRANTY_TO_AMC",
  "effectiveDate": "2025-01-15T00:00:00Z"
}
```

### 5. Process Bulk Conversions

**PUT** `/api/conversions`

Processes multiple conversions in a single request.

**Request Body:**
```json
{
  "conversions": [
    {
      "sourceId": "uuid1",
      "conversionType": "WARRANTY_TO_AMC",
      "reason": "Bulk conversion batch 1",
      "effectiveDate": "2025-01-15T00:00:00Z",
      "amcData": {
        "startDate": "2025-01-15T00:00:00Z",
        "endDate": "2026-01-15T00:00:00Z",
        "amount": 50000,
        "numberOfServices": 4,
        "natureOfService": "Preventive Maintenance"
      }
    }
  ],
  "validateOnly": false
}
```

**Response:**
```json
{
  "results": [
    {
      "success": true,
      "message": "Warranty successfully converted to AMC",
      "sourceId": "uuid1",
      "targetId": "uuid2",
      "historyCardId": "uuid3",
      "conversionType": "WARRANTY_TO_AMC",
      "effectiveDate": "2025-01-15T00:00:00Z"
    }
  ],
  "errors": [],
  "totalRequested": 1,
  "successCount": 1,
  "errorCount": 0
}
```

## Error Responses

All endpoints return appropriate HTTP status codes and error messages:

### 400 Bad Request
```json
{
  "error": "Validation error",
  "details": [
    {
      "code": "invalid_string",
      "message": "Valid source ID is required",
      "path": ["sourceId"]
    }
  ]
}
```

### 401 Unauthorized
```json
{
  "error": "Authentication required"
}
```

### 403 Forbidden
```json
{
  "error": "Insufficient permissions"
}
```

### 404 Not Found
```json
{
  "error": "Source record not found"
}
```

### 409 Conflict
```json
{
  "error": "Conversion already exists or duplicate constraint violation"
}
```

### 500 Internal Server Error
```json
{
  "error": "Failed to process conversion"
}
```

## Validation Rules

### General Rules
- All UUIDs must be valid UUID v4 format
- Dates must be valid ISO 8601 format
- Source records must exist and be in valid state for conversion
- Effective date must be current or future date
- End dates must be after start dates

### Conversion-Specific Rules
- **WARRANTY_TO_AMC**: Warranty must not already be converted
- **AMC_TO_OUT_WARRANTY**: AMC contract must not already be converted
- **WARRANTY_TO_OUT_WARRANTY**: Warranty must not already be converted

## Database Operations

All conversions are performed within database transactions to ensure data integrity:

1. **Validation**: Check source record exists and is eligible
2. **Creation**: Create target record with copied data
3. **Machine/Component Copy**: Transfer related machines and components
4. **History Card**: Create conversion tracking record
5. **Status Update**: Mark source record as converted

## Rate Limiting

- Single conversions: No specific limit
- Bulk conversions: Maximum 50 conversions per request
- API calls are subject to general rate limiting policies

## Testing

Use the following test credentials:
- **Email**: <EMAIL>
- **Password**: Admin@123

Test with existing warranty/AMC records in the database for realistic conversion scenarios.
