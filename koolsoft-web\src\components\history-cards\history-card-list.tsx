'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/components/ui/use-toast';
import { 
  Search, 
  Eye, 
  Edit, 
  Trash2, 
  Download,
  Filter,
  ChevronLeft,
  ChevronRight,
  FileText,
  Calendar,
  User,
} from 'lucide-react';
import { format } from 'date-fns';

interface HistoryCard {
  id: string;
  cardNo?: number;
  source?: 'AMC' | 'INW' | 'OTW';
  customerId: string;
  amcId?: string;
  inWarrantyId?: string;
  outWarrantyId?: string;
  toCardNo?: number;
  originalId?: number;
  createdAt: string;
  updatedAt: string;
  customer?: {
    id: string;
    name: string;
    address?: string;
    city?: string;
    phone?: string;
  };
  sections?: {
    id: string;
    sectionCode: string;
    content: string;
    createdAt: string;
  }[];
}

interface HistoryCardListProps {
  onEditCard?: (card: HistoryCard) => void;
  onDeleteCard?: (cardId: string) => void;
  onBulkAction?: (action: string, selectedIds: string[]) => void;
  showActions?: boolean;
}

export function HistoryCardList({
  onEditCard,
  onDeleteCard,
  onBulkAction,
  showActions = true,
}: HistoryCardListProps) {
  const router = useRouter();
  const { toast } = useToast();
  
  // State management
  const [historyCards, setHistoryCards] = useState<HistoryCard[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedCards, setSelectedCards] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [sourceFilter, setSourceFilter] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [sortBy, setSortBy] = useState<'createdAt' | 'cardNo' | 'source'>('createdAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  // Fetch history cards
  const fetchHistoryCards = async () => {
    try {
      setIsLoading(true);
      
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '10',
        sortBy,
        sortOrder,
      });
      
      if (searchTerm) {
        params.append('search', searchTerm);
      }
      
      if (sourceFilter !== 'all') {
        params.append('source', sourceFilter);
      }
      
      const response = await fetch(`/api/history-cards?${params}`, {
        credentials: 'include',
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch history cards');
      }
      
      const data = await response.json();
      
      if (data.success) {
        setHistoryCards(data.data);
        setTotalPages(data.pagination.totalPages);
        setTotalCount(data.pagination.total);
      } else {
        throw new Error(data.error || 'Failed to fetch history cards');
      }
    } catch (error) {
      console.error('Error fetching history cards:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch history cards. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Effects
  useEffect(() => {
    fetchHistoryCards();
  }, [currentPage, searchTerm, sourceFilter, sortBy, sortOrder]);

  // Event handlers
  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setCurrentPage(1);
  };

  const handleSourceFilter = (value: string) => {
    setSourceFilter(value);
    setCurrentPage(1);
  };

  const handleSort = (field: 'createdAt' | 'cardNo' | 'source') => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('desc');
    }
    setCurrentPage(1);
  };

  const handleSelectCard = (cardId: string, checked: boolean) => {
    if (checked) {
      setSelectedCards([...selectedCards, cardId]);
    } else {
      setSelectedCards(selectedCards.filter(id => id !== cardId));
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedCards(historyCards.map(card => card.id));
    } else {
      setSelectedCards([]);
    }
  };

  const handleViewCard = (cardId: string) => {
    router.push(`/history-cards/${cardId}`);
  };

  const handleEditCard = (card: HistoryCard) => {
    if (onEditCard) {
      onEditCard(card);
    } else {
      router.push(`/history-cards/${card.id}/edit`);
    }
  };

  const handleDeleteCard = async (cardId: string) => {
    if (onDeleteCard) {
      onDeleteCard(cardId);
    } else {
      // Default delete implementation
      try {
        const response = await fetch(`/api/history-cards/${cardId}`, {
          method: 'DELETE',
          credentials: 'include',
        });
        
        if (!response.ok) {
          throw new Error('Failed to delete history card');
        }
        
        toast({
          title: 'Success',
          description: 'History card deleted successfully.',
        });
        
        fetchHistoryCards();
      } catch (error) {
        console.error('Error deleting history card:', error);
        toast({
          title: 'Error',
          description: 'Failed to delete history card. Please try again.',
          variant: 'destructive',
        });
      }
    }
  };

  const handleBulkAction = (action: string) => {
    if (selectedCards.length === 0) {
      toast({
        title: 'Warning',
        description: 'Please select at least one history card.',
        variant: 'destructive',
      });
      return;
    }
    
    if (onBulkAction) {
      onBulkAction(action, selectedCards);
    }
  };

  const getSourceBadgeVariant = (source?: string) => {
    switch (source) {
      case 'AMC':
        return 'default';
      case 'INW':
        return 'secondary';
      case 'OTW':
        return 'outline';
      default:
        return 'outline';
    }
  };

  const getSourceLabel = (source?: string) => {
    switch (source) {
      case 'AMC':
        return 'AMC';
      case 'INW':
        return 'In-Warranty';
      case 'OTW':
        return 'Out-of-Warranty';
      default:
        return 'Unknown';
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Loading History Cards...</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <Skeleton key={i} className="h-16 w-full" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Search and Filter Controls */}
      <Card>
        <CardHeader className="pb-4">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <div className="flex flex-col sm:flex-row gap-4 flex-1">
              <div className="relative flex-1 max-w-sm">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search by customer name or card number..."
                  value={searchTerm}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <Select value={sourceFilter} onValueChange={handleSourceFilter}>
                <SelectTrigger className="w-48">
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="Filter by source" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Sources</SelectItem>
                  <SelectItem value="AMC">AMC</SelectItem>
                  <SelectItem value="INW">In-Warranty</SelectItem>
                  <SelectItem value="OTW">Out-of-Warranty</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            {showActions && selectedCards.length > 0 && (
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleBulkAction('delete')}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete Selected ({selectedCards.length})
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleBulkAction('export')}
                >
                  <Download className="h-4 w-4 mr-2" />
                  Export Selected
                </Button>
              </div>
            )}
          </div>
        </CardHeader>
      </Card>

      {/* History Cards Table */}
      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                {showActions && (
                  <TableHead className="w-12">
                    <Checkbox
                      checked={selectedCards.length === historyCards.length && historyCards.length > 0}
                      onCheckedChange={handleSelectAll}
                    />
                  </TableHead>
                )}
                <TableHead 
                  className="cursor-pointer hover:bg-gray-50"
                  onClick={() => handleSort('cardNo')}
                >
                  Card No. {sortBy === 'cardNo' && (sortOrder === 'asc' ? '↑' : '↓')}
                </TableHead>
                <TableHead>Customer</TableHead>
                <TableHead 
                  className="cursor-pointer hover:bg-gray-50"
                  onClick={() => handleSort('source')}
                >
                  Source {sortBy === 'source' && (sortOrder === 'asc' ? '↑' : '↓')}
                </TableHead>
                <TableHead>Sections</TableHead>
                <TableHead 
                  className="cursor-pointer hover:bg-gray-50"
                  onClick={() => handleSort('createdAt')}
                >
                  Created {sortBy === 'createdAt' && (sortOrder === 'asc' ? '↑' : '↓')}
                </TableHead>
                {showActions && <TableHead className="w-32">Actions</TableHead>}
              </TableRow>
            </TableHeader>
            <TableBody>
              {historyCards.length === 0 ? (
                <TableRow>
                  <TableCell 
                    colSpan={showActions ? 7 : 6} 
                    className="text-center py-8 text-gray-500"
                  >
                    No history cards found.
                  </TableCell>
                </TableRow>
              ) : (
                historyCards.map((card) => (
                  <TableRow key={card.id}>
                    {showActions && (
                      <TableCell>
                        <Checkbox
                          checked={selectedCards.includes(card.id)}
                          onCheckedChange={(checked) => handleSelectCard(card.id, checked as boolean)}
                        />
                      </TableCell>
                    )}
                    <TableCell className="font-medium">
                      {card.cardNo || 'N/A'}
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-col">
                        <span className="font-medium">{card.customer?.name || 'Unknown'}</span>
                        {card.customer?.city && (
                          <span className="text-sm text-gray-500">{card.customer.city}</span>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={getSourceBadgeVariant(card.source)}>
                        {getSourceLabel(card.source)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <span className="text-sm text-gray-600">
                        {card.sections?.length || 0} sections
                      </span>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-col">
                        <span className="text-sm">
                          {format(new Date(card.createdAt), 'MMM dd, yyyy')}
                        </span>
                        <span className="text-xs text-gray-500">
                          {format(new Date(card.createdAt), 'HH:mm')}
                        </span>
                      </div>
                    </TableCell>
                    {showActions && (
                      <TableCell>
                        <div className="flex gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleViewCard(card.id)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditCard(card)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteCard(card.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    )}
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Pagination */}
      {totalPages > 1 && (
        <Card>
          <CardContent className="py-4">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-600">
                Showing {((currentPage - 1) * 10) + 1} to {Math.min(currentPage * 10, totalCount)} of {totalCount} history cards
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(currentPage - 1)}
                  disabled={currentPage === 1}
                >
                  <ChevronLeft className="h-4 w-4" />
                  Previous
                </Button>
                <span className="text-sm">
                  Page {currentPage} of {totalPages}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(currentPage + 1)}
                  disabled={currentPage === totalPages}
                >
                  Next
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
