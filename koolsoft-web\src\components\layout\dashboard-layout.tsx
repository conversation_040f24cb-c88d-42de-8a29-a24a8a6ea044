'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { BaseLayout } from './base-layout';
import { PageHeader } from './page-header';
import { useAuth } from '@/lib/hooks/useAuth';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import {
  LayoutDashboard,
  Users,
  FileText,
  Shield,
  BarChart4,
  Wrench,
  ChevronLeft,
  ChevronRight,
  User,
  LogOut,
  Settings,
  Calendar,
  CreditCard,
  Clock,
  AlertTriangle,
  XCircle,
  Cog,
  ChevronDown,
  ChevronRight as ChevronRightIcon,
  Database,
  Plus,
  ArrowRightLeft,
  Kanban,
  // Add other icons as needed
} from 'lucide-react';

interface NavItem {
  name: string;
  href: string;
  icon: React.ReactNode;
  roles?: string[];
  children?: NavItem[];
}

interface DashboardLayoutProps {
  children: React.ReactNode;
  title: string;
  actions?: React.ReactNode;
  breadcrumbs?: any[];
  showAdminLink?: boolean;
  requireAuth?: boolean;
  allowedRoles?: string[];
}

/**
 * DashboardLayout Component
 *
 * A layout with a collapsible sidebar navigation for the dashboard and related pages.
 */
export function DashboardLayout({
  children,
  title,
  actions,
  breadcrumbs,
  showAdminLink = false,
  requireAuth = true,
  allowedRoles = [],
}: DashboardLayoutProps) {
  const { user, hasRole, logout } = useAuth();
  const pathname = usePathname();
  const router = useRouter();
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [expandedItems, setExpandedItems] = useState<string[]>([]);

  // Navigation items for the sidebar
  const navItems: NavItem[] = [
    {
      name: 'Dashboard',
      href: '/dashboard',
      icon: <LayoutDashboard className="h-5 w-5" />,
    },
    {
      name: 'Customers',
      href: '/customers',
      icon: <Users className="h-5 w-5" />,
    },
    {
      name: 'AMC Management',
      href: '/amc',
      icon: <Calendar className="h-5 w-5" />,
      children: [
        {
          name: 'All Contracts',
          href: '/amc',
          icon: <FileText className="h-4 w-4" />,
        },
        {
          name: 'Payments',
          href: '/amc/payments',
          icon: <CreditCard className="h-4 w-4" />,
        },
        {
          name: 'Service Dates',
          href: '/amc/service-dates',
          icon: <Clock className="h-4 w-4" />,
        },
        {
          name: 'New Contract',
          href: '/amc/new',
          icon: <FileText className="h-4 w-4" />,
          roles: ['ADMIN', 'MANAGER', 'EXECUTIVE'],
        },
      ],
    },
    {
      name: 'Warranty Management',
      href: '/warranties',
      icon: <Shield className="h-5 w-5" />,
      children: [
        {
          name: 'Overview',
          href: '/warranties',
          icon: <Shield className="h-4 w-4" />,
        },
        {
          name: 'In-Warranty',
          href: '/warranties/in-warranty',
          icon: <Shield className="h-4 w-4" />,
        },
        {
          name: 'Out-of-Warranty',
          href: '/warranties/out-warranty',
          icon: <XCircle className="h-4 w-4" />,
        },
        {
          name: 'Components',
          href: '/warranties/components',
          icon: <Cog className="h-4 w-4" />,
        },
        {
          name: 'Status Dashboard',
          href: '/warranties/status',
          icon: <BarChart4 className="h-4 w-4" />,
        },
        {
          name: 'Alerts',
          href: '/warranties/alerts',
          icon: <AlertTriangle className="h-4 w-4" />,
        },
        {
          name: 'BLUESTAR',
          href: '/warranties/bluestar',
          icon: <Shield className="h-4 w-4" />,
        },
      ],
    },
    {
      name: 'History Cards',
      href: '/history-cards',
      icon: <FileText className="h-5 w-5" />,
      children: [
        {
          name: 'All Cards',
          href: '/history-cards',
          icon: <FileText className="h-4 w-4" />,
        },
        {
          name: 'New Card',
          href: '/history-cards/new',
          icon: <Plus className="h-4 w-4" />,
          roles: ['ADMIN', 'MANAGER', 'EXECUTIVE'],
        },
      ],
    },
    {
      name: 'Reference Data',
      href: '/reference-data',
      icon: <Database className="h-5 w-5" />,
      roles: ['ADMIN', 'MANAGER'],
    },
    {
      name: 'Service Management',
      href: '/service',
      icon: <Wrench className="h-5 w-5" />,
      children: [
        {
          name: 'Overview',
          href: '/service',
          icon: <Wrench className="h-4 w-4" />,
        },
        {
          name: 'New Service Report',
          href: '/service/new',
          icon: <Plus className="h-4 w-4" />,
          roles: ['ADMIN', 'MANAGER', 'EXECUTIVE'],
        },
        {
          name: 'Service History',
          href: '/service/history',
          icon: <FileText className="h-4 w-4" />,
        },
        {
          name: 'Service Dashboard',
          href: '/service/dashboard',
          icon: <BarChart4 className="h-4 w-4" />,
        },
        {
          name: 'Service Scheduling',
          href: '/service/scheduling',
          icon: <Calendar className="h-4 w-4" />,
          roles: ['ADMIN', 'MANAGER', 'EXECUTIVE'],
        },
      ],
    },

    {
      name: 'Sales',
      href: '/sales',
      icon: <BarChart4 className="h-5 w-5" />,
      children: [
        {
          name: 'Lead Management',
          href: '/leads',
          icon: <Users className="h-4 w-4" />,
        },
        {
          name: 'Sales Pipeline',
          href: '/sales/pipeline',
          icon: <Kanban className="h-4 w-4" />,
        },
      ],
    },
    {
      name: 'Reports',
      href: '/reports',
      icon: <FileText className="h-5 w-5" />,
      roles: ['ADMIN', 'MANAGER', 'EXECUTIVE'],
      children: [
        {
          name: 'All Reports',
          href: '/reports',
          icon: <FileText className="h-4 w-4" />,
        },
        {
          name: 'Conversion Reports',
          href: '/conversions/reports',
          icon: <ArrowRightLeft className="h-4 w-4" />,
        },
      ],
    },
    {
      name: 'Admin',
      href: '/admin',
      icon: <Settings className="h-5 w-5" />,
      roles: ['ADMIN'],
    },
  ];

  // Handle sign out
  const handleSignOut = async () => {
    await logout();
    router.push('/auth/login');
  };

  // Toggle expanded state for menu items with children
  const toggleExpanded = (href: string) => {
    setExpandedItems(prev =>
      prev.includes(href)
        ? prev.filter(item => item !== href)
        : [...prev, href]
    );
  };

  // Check if a nav item is active
  const isActive = (href: string, children?: NavItem[]) => {
    if (pathname === href) return true;
    if (children) {
      return children.some(child => pathname === child.href || pathname.startsWith(`${child.href}/`));
    }
    return pathname.startsWith(`${href}/`);
  };

  // Check if a nav item should be expanded (active or manually expanded)
  const isExpanded = (href: string, children?: NavItem[]) => {
    if (!children) return false;
    return expandedItems.includes(href) || children.some(child =>
      pathname === child.href || pathname.startsWith(`${child.href}/`)
    );
  };

  // Default profile actions if none provided
  const defaultActions = actions || (
    <div className="flex items-center space-x-2">
      <Button asChild variant="outline" size="sm">
        <Link href="/profile">
          <User className="h-4 w-4 mr-1" />
          My Profile
        </Link>
      </Button>
      <Button
        variant="destructive"
        size="sm"
        onClick={handleSignOut}
      >
        <LogOut className="h-4 w-4 mr-1" />
        Sign Out
      </Button>
    </div>
  );

  return (
    <BaseLayout requireAuth={requireAuth} allowedRoles={allowedRoles}>
      <div className="flex h-screen bg-gray-50 overflow-hidden">
        {/* Sidebar */}
        <div
          className={cn(
            "bg-white h-full shadow-md transition-all duration-300 flex flex-col",
            sidebarCollapsed ? "w-16" : "w-64"
          )}
        >
          {/* Sidebar Header */}
          <div className="p-4 flex items-center justify-between border-b">
            {!sidebarCollapsed && (
              <h2 className="text-lg font-semibold text-gray-800">KoolSoft</h2>
            )}
            <Button
              variant="outline"
              size="icon"
              onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
              aria-label={sidebarCollapsed ? "Expand Sidebar" : "Collapse Sidebar"}
              className="border border-gray-200 bg-white shadow-sm"
            >
              {sidebarCollapsed ? (
                <ChevronRight className="h-5 w-5" />
              ) : (
                <ChevronLeft className="h-5 w-5" />
              )}
            </Button>
          </div>

          {/* Navigation */}
          <div className="flex-1 overflow-y-auto py-4">
            <nav className="space-y-1 px-2">
              {navItems.map((item) => {
                // Skip items that require specific roles if user doesn't have them
                if (item.roles && !item.roles.some(role => hasRole([role]))) {
                  return null;
                }

                const hasChildren = item.children && item.children.length > 0;
                const itemIsActive = isActive(item.href, item.children);
                const itemIsExpanded = isExpanded(item.href, item.children);

                return (
                  <div key={item.href} className="space-y-1">
                    {/* Main nav item */}
                    {hasChildren ? (
                      <button
                        onClick={() => toggleExpanded(item.href)}
                        className={cn(
                          "w-full flex items-center px-3 py-2 rounded-md transition-colors text-left",
                          itemIsActive
                            ? 'bg-primary text-primary-foreground'
                            : 'text-gray-700 hover:bg-secondary',
                          sidebarCollapsed && "justify-center"
                        )}
                      >
                        <span className={sidebarCollapsed ? "" : "mr-2"}>{item.icon}</span>
                        {!sidebarCollapsed && (
                          <>
                            <span className="flex-1">{item.name}</span>
                            <ChevronRightIcon
                              className={cn(
                                "h-4 w-4 transition-transform",
                                itemIsExpanded && "rotate-90"
                              )}
                            />
                          </>
                        )}
                      </button>
                    ) : (
                      <Link
                        href={item.href}
                        className={cn(
                          "flex items-center px-3 py-2 rounded-md transition-colors",
                          itemIsActive
                            ? 'bg-primary text-primary-foreground'
                            : 'text-gray-700 hover:bg-secondary',
                          sidebarCollapsed && "justify-center"
                        )}
                      >
                        <span className={sidebarCollapsed ? "" : "mr-2"}>{item.icon}</span>
                        {!sidebarCollapsed && item.name}
                      </Link>
                    )}

                    {/* Sub-menu items */}
                    {hasChildren && itemIsExpanded && !sidebarCollapsed && (
                      <div className="ml-4 space-y-1">
                        {item.children!.map((child) => {
                          // Skip child items that require specific roles if user doesn't have them
                          if (child.roles && !child.roles.some(role => hasRole([role]))) {
                            return null;
                          }

                          const childIsActive = pathname === child.href || pathname.startsWith(`${child.href}/`);

                          return (
                            <Link
                              key={child.href}
                              href={child.href}
                              className={cn(
                                "flex items-center px-3 py-2 rounded-md transition-colors text-sm",
                                childIsActive
                                  ? 'bg-primary/10 text-primary border-l-2 border-primary'
                                  : 'text-gray-600 hover:bg-secondary hover:text-gray-900'
                              )}
                            >
                              <span className="mr-2">{child.icon}</span>
                              {child.name}
                            </Link>
                          );
                        })}
                      </div>
                    )}
                  </div>
                );
              })}
            </nav>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 flex flex-col overflow-hidden">
          <PageHeader
            title={title}
            breadcrumbs={breadcrumbs}
            actions={defaultActions}
            showDashboardLink={false}
            showAdminLink={showAdminLink}
          />

          <main className="flex-1 overflow-y-auto p-6">
            <div className="max-w-7xl mx-auto">
              {children}
            </div>
          </main>
        </div>
      </div>
    </BaseLayout>
  );
}
