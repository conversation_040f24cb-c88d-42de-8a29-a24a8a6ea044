'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { useToast } from '@/components/ui/use-toast';
import { 
  HistoryCardList, 
  HistoryCardForm, 
  HistoryCardActions 
} from '@/components/history-cards';
import { 
  Plus, 
  FileText, 
  Download,
  Search,
  Filter,
} from 'lucide-react';

interface HistoryCard {
  id: string;
  cardNo?: number;
  source?: 'AMC' | 'INW' | 'OTW';
  customerId: string;
  amcId?: string;
  inWarrantyId?: string;
  outWarrantyId?: string;
  toCardNo?: number;
  originalId?: number;
  createdAt: string;
  updatedAt: string;
  customer?: {
    id: string;
    name: string;
    address?: string;
    city?: string;
    phone?: string;
  };
  sections?: {
    id: string;
    sectionCode: string;
    content: string;
    createdAt: string;
  }[];
}

export default function HistoryCardsPage() {
  const router = useRouter();
  const { toast } = useToast();
  
  const [selectedCards, setSelectedCards] = useState<string[]>([]);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [editingCard, setEditingCard] = useState<HistoryCard | null>(null);
  const [refreshKey, setRefreshKey] = useState(0);

  const handleCreateCard = () => {
    setShowCreateDialog(true);
  };

  const handleEditCard = (card: HistoryCard) => {
    setEditingCard(card);
    setShowEditDialog(true);
  };

  const handleDeleteCard = async (cardId: string) => {
    try {
      const response = await fetch(`/api/history-cards/${cardId}`, {
        method: 'DELETE',
        credentials: 'include',
      });
      
      if (!response.ok) {
        throw new Error('Failed to delete history card');
      }
      
      toast({
        title: 'Success',
        description: 'History card deleted successfully.',
      });
      
      // Refresh the list
      setRefreshKey(prev => prev + 1);
    } catch (error) {
      console.error('Error deleting history card:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete history card. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const handleFormSuccess = (card: HistoryCard) => {
    setShowCreateDialog(false);
    setShowEditDialog(false);
    setEditingCard(null);
    
    // Refresh the list
    setRefreshKey(prev => prev + 1);
    
    toast({
      title: 'Success',
      description: editingCard 
        ? 'History card updated successfully.'
        : 'History card created successfully.',
    });
  };

  const handleFormCancel = () => {
    setShowCreateDialog(false);
    setShowEditDialog(false);
    setEditingCard(null);
  };

  const handleBulkAction = async (action: string, selectedIds: string[]) => {
    if (action === 'export') {
      try {
        const response = await fetch('/api/history-cards/export?format=csv&includeDetails=true', {
          credentials: 'include',
        });
        
        if (!response.ok) {
          throw new Error('Failed to export history cards');
        }
        
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = `history-cards-${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        
        toast({
          title: 'Success',
          description: 'History cards exported successfully.',
        });
      } catch (error) {
        console.error('Error exporting history cards:', error);
        toast({
          title: 'Error',
          description: 'Failed to export history cards. Please try again.',
          variant: 'destructive',
        });
      }
    } else if (action === 'delete') {
      try {
        const response = await fetch('/api/history-cards/bulk', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify({
            action: 'delete',
            ids: selectedIds,
          }),
        });
        
        if (!response.ok) {
          throw new Error('Failed to delete history cards');
        }
        
        const data = await response.json();
        
        if (data.success) {
          toast({
            title: 'Success',
            description: `Successfully deleted ${data.data.deletedCount} history cards.`,
          });
          
          // Clear selection and refresh
          setSelectedCards([]);
          setRefreshKey(prev => prev + 1);
        } else {
          throw new Error(data.error || 'Failed to delete history cards');
        }
      } catch (error) {
        console.error('Error deleting history cards:', error);
        toast({
          title: 'Error',
          description: 'Failed to delete history cards. Please try again.',
          variant: 'destructive',
        });
      }
    }
  };

  const handleRefresh = () => {
    setRefreshKey(prev => prev + 1);
  };

  const handleClearSelection = () => {
    setSelectedCards([]);
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <Card>
        <CardHeader className="pb-3 flex flex-row items-center justify-between bg-primary text-white">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <FileText className="h-5 w-5" />
              <span>History Card Tracking</span>
            </CardTitle>
            <p className="text-gray-100 mt-1">
              Manage and track history cards across conversions from legacy systems
            </p>
          </div>
          <div className="flex space-x-2">
            <Button variant="secondary" onClick={handleCreateCard}>
              <Plus className="h-4 w-4 mr-2" />
              New History Card
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* Bulk Actions */}
      {selectedCards.length > 0 && (
        <Card>
          <CardContent className="py-4">
            <HistoryCardActions
              selectedCardIds={selectedCards}
              onRefresh={handleRefresh}
              onClearSelection={handleClearSelection}
            />
          </CardContent>
        </Card>
      )}

      {/* History Cards List */}
      <HistoryCardList
        key={refreshKey}
        onEditCard={handleEditCard}
        onDeleteCard={handleDeleteCard}
        onBulkAction={handleBulkAction}
        showActions={true}
      />

      {/* Create Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Create New History Card</DialogTitle>
          </DialogHeader>
          <HistoryCardForm
            onSuccess={handleFormSuccess}
            onCancel={handleFormCancel}
          />
        </DialogContent>
      </Dialog>

      {/* Edit Dialog */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit History Card</DialogTitle>
          </DialogHeader>
          <HistoryCardForm
            historyCard={editingCard || undefined}
            onSuccess={handleFormSuccess}
            onCancel={handleFormCancel}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
}
