'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/components/ui/use-toast';
import { HistoryCardForm } from '@/components/history-cards';
import { 
  ArrowLeft, 
  Edit, 
  AlertTriangle,
} from 'lucide-react';

interface HistoryCard {
  id: string;
  cardNo?: number;
  source?: 'AMC' | 'INW' | 'OTW';
  customerId: string;
  amcId?: string;
  inWarrantyId?: string;
  outWarrantyId?: string;
  toCardNo?: number;
  originalId?: number;
  createdAt: string;
  updatedAt: string;
  customer?: {
    id: string;
    name: string;
    address?: string;
    city?: string;
    state?: string;
    phone?: string;
    email?: string;
  };
  sections?: {
    id: string;
    sectionCode: string;
    content: string;
    createdAt: string;
    updatedAt: string;
  }[];
}

interface EditHistoryCardPageProps {
  params: {
    id: string;
  };
}

export default function EditHistoryCardPage({ params }: EditHistoryCardPageProps) {
  const router = useRouter();
  const { toast } = useToast();
  
  const [historyCard, setHistoryCard] = useState<HistoryCard | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch history card details
  useEffect(() => {
    const fetchHistoryCard = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        const response = await fetch(`/api/history-cards/${params.id}`, {
          credentials: 'include',
        });
        
        if (!response.ok) {
          if (response.status === 404) {
            throw new Error('History card not found');
          }
          throw new Error('Failed to fetch history card');
        }
        
        const data = await response.json();
        
        if (data.success) {
          setHistoryCard(data.data);
        } else {
          throw new Error(data.error || 'Failed to fetch history card');
        }
      } catch (error) {
        console.error('Error fetching history card:', error);
        setError(error instanceof Error ? error.message : 'Failed to fetch history card');
        toast({
          title: 'Error',
          description: 'Failed to load history card details. Please try again.',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    if (params.id) {
      fetchHistoryCard();
    }
  }, [params.id, toast]);

  const handleSuccess = (updatedCard: HistoryCard) => {
    toast({
      title: 'Success',
      description: 'History card updated successfully.',
    });
    
    // Navigate back to the detail page
    router.push(`/history-cards/${updatedCard.id}`);
  };

  const handleCancel = () => {
    // Navigate back to the detail page
    router.push(`/history-cards/${params.id}`);
  };

  const handleBack = () => {
    router.push(`/history-cards/${params.id}`);
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <Skeleton className="h-8 w-64" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="py-8">
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <Skeleton key={i} className="h-12 w-full" />
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error || !historyCard) {
    return (
      <div className="space-y-6">
        <Card>
          <CardContent className="py-4">
            <Button variant="outline" onClick={handleBack}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to History Card
            </Button>
          </CardContent>
        </Card>
        
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {error || 'History card not found'}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <Card>
        <CardHeader className="pb-3 flex flex-row items-center justify-between bg-primary text-white">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <Edit className="h-5 w-5" />
              <span>Edit History Card</span>
            </CardTitle>
            <p className="text-gray-100 mt-1">
              Modify details for History Card #{historyCard.cardNo || 'N/A'}
            </p>
          </div>
          <div className="flex space-x-2">
            <Button variant="secondary" onClick={handleBack}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Details
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* Edit Form */}
      <HistoryCardForm
        historyCard={historyCard}
        onSuccess={handleSuccess}
        onCancel={handleCancel}
      />
    </div>
  );
}
