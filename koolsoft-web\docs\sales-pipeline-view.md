# Sales Pipeline View Documentation

## Overview

The Sales Pipeline View is a kanban board interface for managing the sales pipeline in the KoolSoft web application. It provides a visual drag-and-drop interface for tracking sales leads, opportunities, prospects, and orders through different stages of the sales process.

## Features

### Core Functionality
- **Kanban Board Interface**: Visual representation of sales items organized by status
- **Drag-and-Drop**: Move items between columns to update their status
- **Real-time Updates**: Optimistic UI updates with database synchronization
- **Multi-type Support**: Handles leads, opportunities, prospects, and orders
- **Role-based Access**: Supports ADMIN, MANAGER, EXECUTIVE, and USER roles

### Status Columns
The pipeline is organized into seven status columns:
1. **New** - Newly created items
2. **Contacted** - Items where initial contact has been made
3. **Qualified** - Qualified prospects
4. **Proposal** - Proposal stage
5. **Negotiation** - Active negotiations
6. **Closed Won** - Successfully closed deals
7. **Closed Lost** - Lost opportunities

### Data Integration
- **Zero Mock Data**: 100% real database integration using Prisma models
- **API Integration**: Connects to existing sales API endpoints:
  - `/api/sales/leads`
  - `/api/sales/opportunities`
  - `/api/sales/prospects`
  - `/api/sales/orders`
- **Database Updates**: Status changes are immediately persisted to PostgreSQL

## Technical Implementation

### Architecture
- **Framework**: Next.js App Router with TypeScript
- **Drag-and-Drop**: @dnd-kit/core library for accessibility and performance
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: Role-based access control with NextAuth.js
- **UI Components**: shadcn/ui components with Tailwind CSS

### Key Components

#### SalesPipelineBoard
Main kanban board component that:
- Fetches data from all sales endpoints
- Manages drag-and-drop state
- Handles optimistic updates
- Provides error handling and loading states

#### PipelineColumn
Individual column component that:
- Displays items for a specific status
- Handles drop zone functionality
- Shows item counts
- Provides visual feedback during drag operations

#### PipelineCard
Individual sales item card that:
- Displays item information (customer, executive, amount, etc.)
- Provides drag handle and action menu
- Shows type-specific styling and badges
- Links to detailed views

### File Structure
```
src/
├── app/sales/pipeline/
│   └── page.tsx                    # Main pipeline page
├── components/sales/
│   ├── sales-pipeline-board.tsx    # Main kanban board
│   ├── pipeline-column.tsx         # Column component
│   ├── pipeline-card.tsx           # Card component
│   └── index.ts                    # Component exports
```

## Usage

### Navigation
The Sales Pipeline View is accessible through:
- **URL**: `/sales/pipeline`
- **Navigation**: Sales → Sales Pipeline in the left sidebar
- **Breadcrumbs**: Sales → Pipeline

### User Interactions

#### Viewing the Pipeline
- Items are automatically loaded and displayed in their respective status columns
- Each column shows the count of items
- Cards display key information: customer name, executive, contact details, amounts, etc.

#### Moving Items
1. Click and drag any card to move it between columns
2. Drop the card in the target column to update its status
3. The UI updates optimistically while the database is updated
4. Success/error notifications are displayed

#### Card Actions
Each card provides:
- **View Details**: Navigate to the detailed view
- **Edit**: Navigate to the edit form
- **Delete**: Remove the item (with confirmation)

#### Action Bar
The top action bar provides:
- **New Lead**: Create a new sales lead
- **Filter**: Filter items (future enhancement)
- **Export**: Export pipeline data (future enhancement)

## Styling and Design

### Color Scheme
- **Primary Blue**: #0F52BA for headers and primary actions
- **Status Colors**: Each column has a unique color scheme:
  - New: Blue
  - Contacted: Yellow
  - Qualified: Green
  - Proposal: Purple
  - Negotiation: Orange
  - Closed Won: Emerald
  - Closed Lost: Red

### Responsive Design
- **Desktop**: 7-column layout for full pipeline view
- **Tablet**: 4-column layout with horizontal scrolling
- **Mobile**: 2-column layout with stacked columns

### Accessibility
- **Keyboard Navigation**: Full keyboard support via @dnd-kit
- **Screen Readers**: Proper ARIA labels and descriptions
- **Touch Support**: Mobile-friendly drag-and-drop
- **Color Contrast**: Meets WCAG 2.1 AA standards

## Error Handling

### Network Errors
- Failed API calls show error notifications
- Optimistic updates are reverted on failure
- Retry mechanisms for transient failures

### Validation
- Status changes are validated server-side
- Invalid operations are prevented with user feedback
- Role-based restrictions are enforced

### Loading States
- Skeleton loaders during initial data fetch
- Loading indicators during drag operations
- Graceful handling of empty states

## Performance Considerations

### Optimization Techniques
- **Lazy Loading**: Components are loaded on demand
- **Optimistic Updates**: Immediate UI feedback
- **Efficient Queries**: Paginated API calls with take/skip
- **Memoization**: React.memo and useMemo for expensive operations

### Scalability
- **Pagination**: Supports large datasets with pagination
- **Virtual Scrolling**: Future enhancement for very large lists
- **Caching**: API responses are cached for performance

## Security

### Authentication
- **Role-based Access**: ADMIN, MANAGER, EXECUTIVE, USER roles supported
- **Session Management**: Secure session handling with NextAuth.js
- **API Protection**: All endpoints protected with role-based middleware

### Data Protection
- **Input Validation**: All user inputs are validated
- **SQL Injection Prevention**: Prisma ORM provides protection
- **XSS Prevention**: React's built-in XSS protection

## Future Enhancements

### Planned Features
1. **Advanced Filtering**: Filter by date range, executive, customer, amount
2. **Search Functionality**: Global search across all sales items
3. **Bulk Operations**: Select and update multiple items
4. **Export Functionality**: Export pipeline data to CSV/Excel
5. **Real-time Collaboration**: Live updates when multiple users are active
6. **Analytics Dashboard**: Pipeline metrics and conversion rates
7. **Custom Columns**: User-defined status columns
8. **Automation Rules**: Automatic status updates based on criteria

### Technical Improvements
1. **Virtual Scrolling**: Handle thousands of items efficiently
2. **Offline Support**: Work offline with sync when reconnected
3. **Push Notifications**: Real-time updates via WebSockets
4. **Advanced Caching**: Redis caching for improved performance

## Troubleshooting

### Common Issues

#### Drag-and-Drop Not Working
- Ensure JavaScript is enabled
- Check for browser compatibility
- Verify touch events on mobile devices

#### Status Updates Failing
- Check network connectivity
- Verify user permissions
- Review server logs for API errors

#### Performance Issues
- Monitor API response times
- Check database query performance
- Consider pagination limits

### Support
For technical support or feature requests, contact the development team or create an issue in the project repository.

## Changelog

### Version 1.0.0 (Current)
- Initial implementation of Sales Pipeline View
- Kanban board with drag-and-drop functionality
- Integration with existing sales API endpoints
- Role-based access control
- Responsive design
- Real-time status updates
- Error handling and loading states
