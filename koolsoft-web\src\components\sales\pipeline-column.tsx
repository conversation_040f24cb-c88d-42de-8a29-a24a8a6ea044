'use client';

import React from 'react';
import { useDroppable } from '@dnd-kit/core';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { PipelineCard } from './pipeline-card';
import { SalesItem } from './sales-pipeline-board';
import { cn } from '@/lib/utils';

interface PipelineColumnProps {
  id: string;
  title: string;
  color: string;
  headerColor: string;
  items: SalesItem[];
  activeId: string | null;
}

export function PipelineColumn({
  id,
  title,
  color,
  headerColor,
  items,
  activeId,
}: PipelineColumnProps) {
  const { setNodeRef, isOver } = useDroppable({
    id,
  });

  const itemIds = items.map(item => item.id);

  return (
    <Card
      ref={setNodeRef}
      className={cn(
        'h-fit min-h-[400px] transition-colors duration-200',
        color,
        isOver && 'ring-2 ring-primary ring-opacity-50'
      )}
    >
      <CardHeader className={cn('pb-3', headerColor)}>
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm font-medium">
            {title}
          </CardTitle>
          <Badge variant="secondary" className="bg-white/20 text-white">
            {items.length}
          </Badge>
        </div>
      </CardHeader>
      
      <CardContent className="p-3 space-y-3">
        <SortableContext items={itemIds} strategy={verticalListSortingStrategy}>
          {items.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <p className="text-sm">No items</p>
              <p className="text-xs">Drag items here</p>
            </div>
          ) : (
            items.map((item) => (
              <PipelineCard
                key={item.id}
                item={item}
                isActive={activeId === item.id}
              />
            ))
          )}
        </SortableContext>
      </CardContent>
    </Card>
  );
}
