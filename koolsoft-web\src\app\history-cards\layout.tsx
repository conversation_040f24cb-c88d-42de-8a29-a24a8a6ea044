'use client';

import { usePathname } from 'next/navigation';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { BreadcrumbItemType } from '@/components/layout/page-header';
import { FileText, Plus, Eye, Edit } from 'lucide-react';

/**
 * History Cards Layout Component
 *
 * This component provides a consistent layout for all history card-related pages
 * using the standardized DashboardLayout component with collapsible sidebar.
 */
export default function HistoryCardsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();

  // Determine the current page title and breadcrumbs based on the pathname
  let pageTitle = 'History Card Tracking';
  let breadcrumbs: BreadcrumbItemType[] = [
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'History Cards', href: '/history-cards', icon: <FileText className="h-4 w-4" /> }
  ];

  // Add specific breadcrumbs based on the current path
  if (pathname === '/history-cards/new') {
    pageTitle = 'Create New History Card';
    breadcrumbs.push({
      label: 'New History Card',
      href: '/history-cards/new',
      icon: <Plus className="h-4 w-4" />,
      current: true
    });
  } else if (pathname.match(/^\/history-cards\/[^\/]+$/)) {
    // Individual history card view
    pageTitle = 'History Card Details';
    breadcrumbs.push({
      label: 'Details',
      href: pathname,
      icon: <Eye className="h-4 w-4" />,
      current: true
    });
  } else if (pathname.match(/^\/history-cards\/[^\/]+\/edit$/)) {
    // Edit history card
    pageTitle = 'Edit History Card';
    const cardId = pathname.split('/')[2];
    breadcrumbs.push(
      {
        label: 'Details',
        href: `/history-cards/${cardId}`,
        icon: <Eye className="h-4 w-4" />
      },
      {
        label: 'Edit',
        href: pathname,
        icon: <Edit className="h-4 w-4" />,
        current: true
      }
    );
  } else if (pathname === '/history-cards') {
    // Main history cards page
    breadcrumbs[breadcrumbs.length - 1].current = true;
  }

  return (
    <DashboardLayout
      title={pageTitle}
      breadcrumbs={breadcrumbs}
      requireAuth={true}
      allowedRoles={['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER']}
    >
      {children}
    </DashboardLayout>
  );
}
