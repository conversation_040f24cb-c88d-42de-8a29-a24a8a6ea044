Implement Task 11.2: Develop Lead Management Interface for lead tracking in the KoolSoft web application. Follow the mandatory 6-step workflow:

1. **Task Analysis & Planning**: Research existing codebase patterns, database schema for leads/customers, and review dependencies (tasks 5.1, 5.2, 5.3, 11.1). Plan the interface architecture following established KoolSoft UI standards.

2. **Implementation**: Create a comprehensive lead management interface with:
   - CRUD operations for lead tracking using Prisma models and real database integration
   - TypeScript implementation with Zod validation schemas
   - Role-based access control (Admin/Manager/Executive access levels)
   - DashboardLayout with primary blue header (#0F52BA), proper breadcrumbs, and action buttons
   - Searchable dropdown components for large datasets
   - Export functionality and bulk operations
   - Consistent UI patterns matching other KoolSoft modules

3. **Testing**: Test all functionality using admin credentials (<EMAIL> / Admin@123)

4. **Error Resolution**: Fix any issues found during testing

5. **Documentation**: Update docs folder with implementation details and API documentation

6. **Git Operations**: Commit with format 'feat: implement Lead Management Interface (lead tracking, CRUD operations, role-based access)'

Requirements: Zero tolerance for mock data - use 100% real database integration, follow established color scheme and layout patterns, ensure responsive design, and maintain consistency with existing AMC/Warranty management modules.


Refer necessary documents, code and Plan meticulously and code | 11.2 | Develop Lead Management Interface | Create interface for lead tracking 