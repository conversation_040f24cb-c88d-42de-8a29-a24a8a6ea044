import { NextRequest, NextResponse } from 'next/server';
import { getOutWarrantyRepository } from '@/lib/repositories';
import { outWarrantyExportSchema } from '@/lib/validations/warranty.schema';
import { withRoleProtection } from '@/lib/auth/middleware';
import { z } from 'zod';

/**
 * GET /api/out-warranties/export
 * Export out-warranties data in various formats
 */
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  async (request: NextRequest) => {
    try {
      const { searchParams } = new URL(request.url);
      
      // Parse and validate query parameters
      const params = Object.fromEntries(searchParams.entries());
      const validatedParams = outWarrantyExportSchema.parse(params);

      const outWarrantyRepository = getOutWarrantyRepository();

      // Build filter object from export filters
      const filter: any = {};
      
      if (validatedParams.filters) {
        const filters = validatedParams.filters;
        
        if (filters.customerId) {
          filter.customerId = filters.customerId;
        }
        
        if (filters.executiveId) {
          filter.executiveId = filters.executiveId;
        }
        
        if (filters.source) {
          filter.source = filters.source;
        }
        
        if (filters.isActive !== undefined) {
          filter.isActive = filters.isActive;
        }
        
        if (filters.search) {
          filter.OR = [
            {
              customer: {
                name: {
                  contains: filters.search,
                  mode: 'insensitive',
                },
              },
            },
            {
              contactPerson: {
                contains: filters.search,
                mode: 'insensitive',
              },
            },
          ];
        }
        
        // Date range filters
        if (filters.startDateFrom || filters.startDateTo) {
          filter.startDate = {};
          if (filters.startDateFrom) {
            filter.startDate.gte = filters.startDateFrom;
          }
          if (filters.startDateTo) {
            filter.startDate.lte = filters.startDateTo;
          }
        }
        
        if (filters.endDateFrom || filters.endDateTo) {
          filter.endDate = {};
          if (filters.endDateFrom) {
            filter.endDate.gte = filters.endDateFrom;
          }
          if (filters.endDateTo) {
            filter.endDate.lte = filters.endDateTo;
          }
        }
      }

      // Get out-warranties with related data
      const outWarranties = await outWarrantyRepository.findWithRelations(
        filter,
        0, // skip
        1000, // take - limit for export
        { [validatedParams.filters?.sortBy || 'startDate']: validatedParams.filters?.sortOrder || 'desc' }
      );

      // Generate CSV content
      if (validatedParams.format === 'CSV') {
        const headers = [
          'ID',
          'Customer Name',
          'Customer City',
          'Executive Name',
          'Contact Person',
          'Contact Phone',
          'Start Date',
          'End Date',
          'Amount',
          'Source',
          'Is Active',
          'Created At',
          'Updated At'
        ];

        if (validatedParams.includePayments) {
          headers.push('Total Payments', 'Balance');
        }

        if (validatedParams.includeMachines) {
          headers.push('Machine Count', 'Machine Models');
        }

        const csvRows = [headers.join(',')];

        outWarranties.forEach((outWarranty: any) => {
          const row = [
            outWarranty.id,
            `"${outWarranty.customer?.name || ''}"`,
            `"${outWarranty.customer?.city || ''}"`,
            `"${outWarranty.executive?.name || ''}"`,
            `"${outWarranty.contactPerson || ''}"`,
            `"${outWarranty.contactPhone || ''}"`,
            outWarranty.startDate ? new Date(outWarranty.startDate).toLocaleDateString() : '',
            outWarranty.endDate ? new Date(outWarranty.endDate).toLocaleDateString() : '',
            outWarranty.amount || 0,
            `"${outWarranty.source || ''}"`,
            outWarranty.isActive ? 'Yes' : 'No',
            outWarranty.createdAt ? new Date(outWarranty.createdAt).toLocaleDateString() : '',
            outWarranty.updatedAt ? new Date(outWarranty.updatedAt).toLocaleDateString() : ''
          ];

          if (validatedParams.includePayments) {
            const totalPayments = outWarranty.payments?.reduce((sum: number, payment: any) => sum + (payment.amount || 0), 0) || 0;
            const balance = (outWarranty.amount || 0) - totalPayments;
            row.push(totalPayments.toString(), balance.toString());
          }

          if (validatedParams.includeMachines) {
            const machineCount = outWarranty.machines?.length || 0;
            const machineModels = outWarranty.machines?.map((machine: any) => machine.model?.name || 'Unknown').join('; ') || '';
            row.push(machineCount.toString(), `"${machineModels}"`);
          }

          csvRows.push(row.join(','));
        });

        const csvContent = csvRows.join('\n');
        const blob = new Blob([csvContent], { type: 'text/csv' });

        return new NextResponse(blob, {
          headers: {
            'Content-Type': 'text/csv',
            'Content-Disposition': `attachment; filename="out-warranties-${new Date().toISOString().split('T')[0]}.csv"`,
          },
        });
      }

      // For other formats, return JSON for now
      return NextResponse.json({
        data: outWarranties,
        total: outWarranties.length,
        format: validatedParams.format,
      });

    } catch (error) {
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { error: 'Invalid export parameters', details: error.errors },
          { status: 400 }
        );
      }

      console.error('Error exporting out-warranties:', error);
      return NextResponse.json(
        { error: 'Failed to export out-warranties' },
        { status: 500 }
      );
    }
  }
);
