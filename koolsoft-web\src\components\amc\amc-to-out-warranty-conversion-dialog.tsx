'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { format } from 'date-fns';
import { CalendarIcon } from 'lucide-react';
import { cn } from '@/lib/utils';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Calendar } from '@/components/ui/calendar';
import { showSuccessToast, showErrorToast } from '@/lib/toast';

// Validation schema for AMC to Out-Warranty conversion
const amcToOutWarrantyConversionSchema = z.object({
  reason: z.string().min(1, 'Conversion reason is required').max(500),
  effectiveDate: z.date({ required_error: 'Effective date is required' }),
  notes: z.string().max(1000).optional(),
  outWarrantyData: z.object({
    startDate: z.date({ required_error: 'Out-warranty start date is required' }),
    endDate: z.date({ required_error: 'Out-warranty end date is required' }),
    executiveId: z.string().uuid().optional(),
    contactPersonId: z.string().uuid().optional(),
    technicianId: z.string().uuid().optional(),
  }).refine(
    (data) => data.endDate > data.startDate,
    {
      message: 'Out-warranty end date must be after start date',
      path: ['endDate'],
    }
  ),
}).refine(
  (data) => data.outWarrantyData.startDate >= data.effectiveDate,
  {
    message: 'Out-warranty start date must be on or after effective date',
    path: ['outWarrantyData', 'startDate'],
  }
);

type AmcToOutWarrantyConversionForm = z.infer<typeof amcToOutWarrantyConversionSchema>;

interface AmcToOutWarrantyConversionDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  amcId: string;
  amcDetails?: {
    id: string;
    customerId: string;
    customerName: string;
    contractNumber?: string;
    numberOfMachines?: number;
    executiveId?: string;
    contactPersonId?: string;
    endDate: string;
  };
  onSuccess?: () => void;
}

export function AmcToOutWarrantyConversionDialog({
  open,
  onOpenChange,
  amcId,
  amcDetails,
  onSuccess,
}: AmcToOutWarrantyConversionDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<AmcToOutWarrantyConversionForm>({
    resolver: zodResolver(amcToOutWarrantyConversionSchema),
    defaultValues: {
      reason: '',
      effectiveDate: new Date(),
      notes: '',
      outWarrantyData: {
        startDate: new Date(),
        endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
        executiveId: amcDetails?.executiveId || undefined,
        contactPersonId: amcDetails?.contactPersonId || undefined,
        technicianId: undefined,
      },
    },
  });

  const onSubmit = async (data: AmcToOutWarrantyConversionForm) => {
    setIsSubmitting(true);
    try {
      const response = await fetch('/api/conversions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          sourceId: amcId,
          conversionType: 'AMC_TO_OUT_WARRANTY',
          reason: data.reason,
          effectiveDate: data.effectiveDate.toISOString(),
          notes: data.notes,
          outWarrantyData: {
            startDate: data.outWarrantyData.startDate.toISOString(),
            endDate: data.outWarrantyData.endDate.toISOString(),
            executiveId: data.outWarrantyData.executiveId,
            contactPersonId: data.outWarrantyData.contactPersonId,
            technicianId: data.outWarrantyData.technicianId,
          },
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to convert AMC to out-warranty');
      }

      const result = await response.json();
      showSuccessToast('AMC successfully converted to out-warranty');
      onOpenChange(false);
      form.reset();
      onSuccess?.();
    } catch (error) {
      console.error('Error converting AMC to out-warranty:', error);
      showErrorToast(error instanceof Error ? error.message : 'Failed to convert AMC to out-warranty');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Convert AMC to Out-of-Warranty</DialogTitle>
          <DialogDescription>
            Convert AMC contract {amcDetails?.contractNumber || amcId.slice(0, 8)} for {amcDetails?.customerName} to out-of-warranty service.
            This action will mark the AMC as converted and create a new out-of-warranty record.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="space-y-4">
              <FormField
                control={form.control}
                name="reason"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Conversion Reason *</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="e.g., AMC contract expired, customer request"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Provide a reason for converting this AMC to out-of-warranty
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="effectiveDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Effective Date *</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              'w-full pl-3 text-left font-normal',
                              !field.value && 'text-muted-foreground'
                            )}
                          >
                            {field.value ? (
                              format(field.value, 'PPP')
                            ) : (
                              <span>Pick a date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) => date > new Date() || date < new Date('1900-01-01')}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormDescription>
                      Date when the conversion takes effect
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Additional Notes</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Any additional information about this conversion..."
                        className="min-h-[80px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="border-t pt-4">
              <h3 className="text-lg font-medium mb-4">Out-of-Warranty Details</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="outWarrantyData.startDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Out-Warranty Start Date *</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant="outline"
                              className={cn(
                                'w-full pl-3 text-left font-normal',
                                !field.value && 'text-muted-foreground'
                              )}
                            >
                              {field.value ? (
                                format(field.value, 'PPP')
                              ) : (
                                <span>Pick a date</span>
                              )}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) => date < new Date('1900-01-01')}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="outWarrantyData.endDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Out-Warranty End Date *</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant="outline"
                              className={cn(
                                'w-full pl-3 text-left font-normal',
                                !field.value && 'text-muted-foreground'
                              )}
                            >
                              {field.value ? (
                                format(field.value, 'PPP')
                              ) : (
                                <span>Pick a date</span>
                              )}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) => date < new Date('1900-01-01')}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? 'Converting...' : 'Convert to Out-Warranty'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
