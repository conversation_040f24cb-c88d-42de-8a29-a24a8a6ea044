"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/leads/page",{

/***/ "(app-pages-browser)/./src/app/leads/page.tsx":
/*!********************************!*\
  !*** ./src/app/leads/page.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LeadsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./src/components/ui/use-toast.tsx\");\n/* harmony import */ var _components_ui_data_table__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/data-table */ \"(app-pages-browser)/./src/components/ui/data-table.tsx\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_MoreHorizontal_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye,MoreHorizontal,Plus,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ellipsis.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_MoreHorizontal_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye,MoreHorizontal,Plus,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_MoreHorizontal_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye,MoreHorizontal,Plus,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_MoreHorizontal_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye,MoreHorizontal,Plus,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_MoreHorizontal_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye,MoreHorizontal,Plus,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_MoreHorizontal_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye,MoreHorizontal,Plus,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_MoreHorizontal_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye,MoreHorizontal,Plus,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\nvar _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Types\n// Status options\nconst statusOptions = [\n    {\n        value: 'all',\n        label: 'All Statuses'\n    },\n    {\n        value: 'NEW',\n        label: 'New'\n    },\n    {\n        value: 'CONTACTED',\n        label: 'Contacted'\n    },\n    {\n        value: 'QUALIFIED',\n        label: 'Qualified'\n    },\n    {\n        value: 'PROPOSAL',\n        label: 'Proposal'\n    },\n    {\n        value: 'NEGOTIATION',\n        label: 'Negotiation'\n    },\n    {\n        value: 'CLOSED_WON',\n        label: 'Closed Won'\n    },\n    {\n        value: 'CLOSED_LOST',\n        label: 'Closed Lost'\n    }\n];\n// Status badge colors\nconst getStatusColor = (status)=>{\n    switch(status){\n        case 'NEW':\n            return 'bg-blue-100 text-blue-800';\n        case 'CONTACTED':\n            return 'bg-yellow-100 text-yellow-800';\n        case 'QUALIFIED':\n            return 'bg-green-100 text-green-800';\n        case 'PROPOSAL':\n            return 'bg-purple-100 text-purple-800';\n        case 'NEGOTIATION':\n            return 'bg-orange-100 text-orange-800';\n        case 'CLOSED_WON':\n            return 'bg-green-100 text-green-800';\n        case 'CLOSED_LOST':\n            return 'bg-red-100 text-red-800';\n        default:\n            return 'bg-gray-100 text-gray-800';\n    }\n};\nfunction LeadsPage() {\n    _s();\n    _s1();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast)();\n    const [leads, setLeads] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [totalCount, setTotalCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [pageSize, setPageSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(10);\n    // Filter states\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [startDate, setStartDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [endDate, setEndDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Bulk operations states\n    const [selectedLeads, setSelectedLeads] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [bulkLoading, setBulkLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Fetch leads data\n    const fetchLeads = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"LeadsPage.useCallback[fetchLeads]\": async ()=>{\n            try {\n                setLoading(true);\n                const params = new URLSearchParams({\n                    skip: ((currentPage - 1) * pageSize).toString(),\n                    take: pageSize.toString(),\n                    sortBy: 'leadDate',\n                    sortOrder: 'desc'\n                });\n                if (searchTerm) params.append('search', searchTerm);\n                if (statusFilter && statusFilter !== 'all') params.append('status', statusFilter);\n                if (startDate) params.append('startDate', startDate);\n                if (endDate) params.append('endDate', endDate);\n                const response = await fetch(\"/api/sales/leads?\".concat(params.toString()), {\n                    credentials: 'include'\n                });\n                if (!response.ok) {\n                    throw new Error('Failed to fetch leads');\n                }\n                const result = await response.json();\n                if (result.success) {\n                    var _result_pagination, _result_pagination1;\n                    setLeads(result.data || []);\n                    setTotalCount(((_result_pagination = result.pagination) === null || _result_pagination === void 0 ? void 0 : _result_pagination.total) || 0);\n                    setTotalPages(((_result_pagination1 = result.pagination) === null || _result_pagination1 === void 0 ? void 0 : _result_pagination1.pages) || 1);\n                } else {\n                    throw new Error('Failed to load leads');\n                }\n            } catch (error) {\n                console.error('Error fetching leads:', error);\n                toast({\n                    title: 'Error',\n                    description: 'Failed to load leads. Please try again.',\n                    variant: 'destructive'\n                });\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"LeadsPage.useCallback[fetchLeads]\"], [\n        currentPage,\n        pageSize,\n        searchTerm,\n        statusFilter,\n        startDate,\n        endDate,\n        toast\n    ]);\n    // Load leads on component mount and when filters change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LeadsPage.useEffect\": ()=>{\n            fetchLeads();\n        }\n    }[\"LeadsPage.useEffect\"], [\n        fetchLeads\n    ]);\n    // Handle search\n    const handleSearch = ()=>{\n        setCurrentPage(1);\n        fetchLeads();\n    };\n    // Clear filters\n    const clearFilters = ()=>{\n        setSearchTerm('');\n        setStatusFilter('all');\n        setStartDate('');\n        setEndDate('');\n        setCurrentPage(1);\n    };\n    // Export leads\n    const handleExport = async ()=>{\n        try {\n            const params = new URLSearchParams();\n            if (searchTerm) params.append('search', searchTerm);\n            if (statusFilter && statusFilter !== 'all') params.append('status', statusFilter);\n            if (startDate) params.append('startDate', startDate);\n            if (endDate) params.append('endDate', endDate);\n            params.append('format', 'CSV');\n            const response = await fetch(\"/api/sales/leads/export?\".concat(params.toString()), {\n                credentials: 'include'\n            });\n            if (!response.ok) {\n                throw new Error('Failed to export leads');\n            }\n            const blob = await response.blob();\n            const url = window.URL.createObjectURL(blob);\n            const a = document.createElement('a');\n            a.style.display = 'none';\n            a.href = url;\n            a.download = \"leads-export-\".concat((0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_13__.format)(new Date(), 'yyyy-MM-dd'), \".csv\");\n            document.body.appendChild(a);\n            a.click();\n            window.URL.revokeObjectURL(url);\n            document.body.removeChild(a);\n            toast({\n                title: 'Success',\n                description: 'Leads exported successfully.'\n            });\n        } catch (error) {\n            console.error('Error exporting leads:', error);\n            toast({\n                title: 'Error',\n                description: 'Failed to export leads. Please try again.',\n                variant: 'destructive'\n            });\n        }\n    };\n    // Delete lead\n    const handleDelete = async (leadId)=>{\n        if (!confirm('Are you sure you want to delete this lead?')) {\n            return;\n        }\n        try {\n            const response = await fetch(\"/api/sales/leads/\".concat(leadId), {\n                method: 'DELETE',\n                credentials: 'include'\n            });\n            if (!response.ok) {\n                throw new Error('Failed to delete lead');\n            }\n            toast({\n                title: 'Success',\n                description: 'Lead deleted successfully.'\n            });\n            fetchLeads(); // Refresh the list\n        } catch (error) {\n            console.error('Error deleting lead:', error);\n            toast({\n                title: 'Error',\n                description: 'Failed to delete lead. Please try again.',\n                variant: 'destructive'\n            });\n        }\n    };\n    // Bulk delete leads\n    const handleBulkDelete = async ()=>{\n        if (selectedLeads.length === 0) {\n            toast({\n                title: 'Error',\n                description: 'Please select leads to delete.',\n                variant: 'destructive'\n            });\n            return;\n        }\n        if (!confirm(\"Are you sure you want to delete \".concat(selectedLeads.length, \" selected leads?\"))) {\n            return;\n        }\n        try {\n            setBulkLoading(true);\n            const response = await fetch('/api/sales/leads/bulk', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                credentials: 'include',\n                body: JSON.stringify({\n                    action: 'DELETE',\n                    leadIds: selectedLeads\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to delete leads');\n            }\n            const result = await response.json();\n            toast({\n                title: 'Success',\n                description: result.message || 'Leads deleted successfully.'\n            });\n            setSelectedLeads([]);\n            fetchLeads(); // Refresh the list\n        } catch (error) {\n            console.error('Error deleting leads:', error);\n            toast({\n                title: 'Error',\n                description: 'Failed to delete leads. Please try again.',\n                variant: 'destructive'\n            });\n        } finally{\n            setBulkLoading(false);\n        }\n    };\n    // Bulk update status\n    const handleBulkStatusUpdate = async (newStatus)=>{\n        if (selectedLeads.length === 0) {\n            toast({\n                title: 'Error',\n                description: 'Please select leads to update.',\n                variant: 'destructive'\n            });\n            return;\n        }\n        try {\n            setBulkLoading(true);\n            const response = await fetch('/api/sales/leads/bulk', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                credentials: 'include',\n                body: JSON.stringify({\n                    action: 'UPDATE_STATUS',\n                    leadIds: selectedLeads,\n                    status: newStatus\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to update lead status');\n            }\n            const result = await response.json();\n            toast({\n                title: 'Success',\n                description: result.message || 'Lead status updated successfully.'\n            });\n            setSelectedLeads([]);\n            fetchLeads(); // Refresh the list\n        } catch (error) {\n            console.error('Error updating lead status:', error);\n            toast({\n                title: 'Error',\n                description: 'Failed to update lead status. Please try again.',\n                variant: 'destructive'\n            });\n        } finally{\n            setBulkLoading(false);\n        }\n    };\n    // Bulk export selected leads\n    const handleBulkExport = async ()=>{\n        if (selectedLeads.length === 0) {\n            toast({\n                title: 'Error',\n                description: 'Please select leads to export.',\n                variant: 'destructive'\n            });\n            return;\n        }\n        try {\n            setBulkLoading(true);\n            const response = await fetch('/api/sales/leads/bulk', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                credentials: 'include',\n                body: JSON.stringify({\n                    action: 'EXPORT',\n                    leadIds: selectedLeads,\n                    exportFormat: 'CSV'\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to export leads');\n            }\n            const blob = await response.blob();\n            const url = window.URL.createObjectURL(blob);\n            const a = document.createElement('a');\n            a.style.display = 'none';\n            a.href = url;\n            a.download = \"leads-bulk-export-\".concat((0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_13__.format)(new Date(), 'yyyy-MM-dd'), \".csv\");\n            document.body.appendChild(a);\n            a.click();\n            window.URL.revokeObjectURL(url);\n            document.body.removeChild(a);\n            toast({\n                title: 'Success',\n                description: 'Selected leads exported successfully.'\n            });\n        } catch (error) {\n            console.error('Error exporting leads:', error);\n            toast({\n                title: 'Error',\n                description: 'Failed to export leads. Please try again.',\n                variant: 'destructive'\n            });\n        } finally{\n            setBulkLoading(false);\n        }\n    };\n    // Table columns definition\n    const columns = [\n        {\n            id: 'select',\n            header: (param)=>{\n                let { table } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                    type: \"checkbox\",\n                    checked: table.getIsAllPageRowsSelected(),\n                    onChange: (e)=>{\n                        table.toggleAllPageRowsSelected(e.target.checked);\n                        if (e.target.checked) {\n                            setSelectedLeads(leads.map((lead)=>lead.id));\n                        } else {\n                            setSelectedLeads([]);\n                        }\n                    },\n                    className: \"rounded border-gray-300\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                    lineNumber: 370,\n                    columnNumber: 11\n                }, this);\n            },\n            cell: (param)=>{\n                let { row } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                    type: \"checkbox\",\n                    checked: selectedLeads.includes(row.original.id),\n                    onChange: (e)=>{\n                        if (e.target.checked) {\n                            setSelectedLeads((prev)=>[\n                                    ...prev,\n                                    row.original.id\n                                ]);\n                        } else {\n                            setSelectedLeads((prev)=>prev.filter((id)=>id !== row.original.id));\n                        }\n                    },\n                    className: \"rounded border-gray-300\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                    lineNumber: 380,\n                    columnNumber: 11\n                }, this);\n            }\n        },\n        {\n            accessorKey: 'customer.name',\n            header: 'Customer',\n            cell: (param)=>{\n                let { row } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"font-medium\",\n                            children: row.original.customer.name\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                            lineNumber: 393,\n                            columnNumber: 11\n                        }, this),\n                        row.original.customer.city && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm text-gray-500\",\n                            children: row.original.customer.city\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                            lineNumber: 394,\n                            columnNumber: 42\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                    lineNumber: 392,\n                    columnNumber: 11\n                }, this);\n            }\n        },\n        {\n            accessorKey: 'contactPerson',\n            header: 'Contact Person',\n            cell: (param)=>{\n                let { row } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col\",\n                    children: [\n                        row.original.contactPerson && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"font-medium\",\n                            children: row.original.contactPerson\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                            lineNumber: 402,\n                            columnNumber: 42\n                        }, this),\n                        row.original.contactPhone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm text-gray-500\",\n                            children: row.original.contactPhone\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                            lineNumber: 403,\n                            columnNumber: 41\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                    lineNumber: 401,\n                    columnNumber: 11\n                }, this);\n            }\n        },\n        {\n            accessorKey: 'executive.name',\n            header: 'Executive',\n            cell: (param)=>{\n                let { row } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"font-medium\",\n                            children: row.original.executive.name\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                            lineNumber: 411,\n                            columnNumber: 11\n                        }, this),\n                        row.original.executive.designation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm text-gray-500\",\n                            children: row.original.executive.designation\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                            lineNumber: 412,\n                            columnNumber: 50\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                    lineNumber: 410,\n                    columnNumber: 11\n                }, this);\n            }\n        },\n        {\n            accessorKey: 'leadDate',\n            header: 'Lead Date',\n            cell: (param)=>{\n                let { row } = param;\n                return (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_13__.format)(new Date(row.original.leadDate), 'MMM dd, yyyy');\n            }\n        },\n        {\n            accessorKey: 'status',\n            header: 'Status',\n            cell: (param)=>{\n                let { row } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                    className: getStatusColor(row.original.status),\n                    children: row.original.status.replace('_', ' ')\n                }, void 0, false, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                    lineNumber: 425,\n                    columnNumber: 11\n                }, this);\n            }\n        },\n        {\n            accessorKey: 'prospectPercentage',\n            header: 'Prospect %',\n            cell: (param)=>{\n                let { row } = param;\n                return row.original.prospectPercentage ? \"\".concat(row.original.prospectPercentage, \"%\") : '-';\n            }\n        },\n        {\n            accessorKey: 'followUpDate',\n            header: 'Follow Up',\n            cell: (param)=>{\n                let { row } = param;\n                return row.original.followUpDate ? (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_13__.format)(new Date(row.original.followUpDate), 'MMM dd, yyyy') : '-';\n            }\n        },\n        {\n            id: 'actions',\n            header: 'Actions',\n            cell: (param)=>{\n                let { row } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_12__.DropdownMenu, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_12__.DropdownMenuTrigger, {\n                            asChild: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                className: \"h-8 w-8 p-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_MoreHorizontal_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                                    lineNumber: 448,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                                lineNumber: 447,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                            lineNumber: 446,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_12__.DropdownMenuContent, {\n                            align: \"end\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_12__.DropdownMenuItem, {\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_11___default()), {\n                                        href: \"/leads/\".concat(row.original.id),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_MoreHorizontal_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                                                lineNumber: 454,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"View Details\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                                        lineNumber: 453,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                                    lineNumber: 452,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_12__.DropdownMenuItem, {\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_11___default()), {\n                                        href: \"/leads/\".concat(row.original.id, \"/edit\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_MoreHorizontal_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                                                lineNumber: 460,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Edit\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                                        lineNumber: 459,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                                    lineNumber: 458,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_12__.DropdownMenuItem, {\n                                    onClick: ()=>handleDelete(row.original.id),\n                                    className: \"text-red-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_MoreHorizontal_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                                            lineNumber: 465,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Delete\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                                    lineNumber: 464,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                            lineNumber: 451,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                    lineNumber: 445,\n                    columnNumber: 11\n                }, this);\n            }\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                    className: \"bg-primary text-primary-foreground\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"text-white\",\n                                        children: \"Lead Management\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                                        lineNumber: 477,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        className: \"text-primary-foreground/80\",\n                                        children: \"Manage and track sales leads\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                                        lineNumber: 478,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                                lineNumber: 476,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                asChild: true,\n                                variant: \"secondary\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_11___default()), {\n                                    href: \"/leads/new\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_MoreHorizontal_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                                            lineNumber: 484,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"New Lead\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                                    lineNumber: 483,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                                lineNumber: 482,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                        lineNumber: 475,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                    lineNumber: 474,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                lineNumber: 473,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"text-black\",\n                            children: \"Filters\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                            lineNumber: 495,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                        lineNumber: 494,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                htmlFor: \"search\",\n                                                className: \"text-black\",\n                                                children: \"Search\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                                                lineNumber: 500,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_MoreHorizontal_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                                                        lineNumber: 502,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                        id: \"search\",\n                                                        placeholder: \"Search customers, contacts...\",\n                                                        value: searchTerm,\n                                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                                        className: \"pl-10\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                                                        lineNumber: 503,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                                                lineNumber: 501,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                                        lineNumber: 499,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                htmlFor: \"status\",\n                                                className: \"text-black\",\n                                                children: \"Status\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                                                lineNumber: 508,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                value: statusFilter,\n                                                onValueChange: setStatusFilter,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                            placeholder: \"Select status\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                                                            lineNumber: 511,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                                                        lineNumber: 510,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                        children: statusOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                value: option.value,\n                                                                children: option.label\n                                                            }, option.value, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                                                                lineNumber: 514,\n                                                                columnNumber: 48\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                                                        lineNumber: 513,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                                                lineNumber: 509,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                                        lineNumber: 507,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                htmlFor: \"startDate\",\n                                                className: \"text-black\",\n                                                children: \"Start Date\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                                                lineNumber: 522,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                id: \"startDate\",\n                                                type: \"date\",\n                                                value: startDate,\n                                                onChange: (e)=>setStartDate(e.target.value)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                                                lineNumber: 523,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                                        lineNumber: 521,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                htmlFor: \"endDate\",\n                                                className: \"text-black\",\n                                                children: \"End Date\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                                                lineNumber: 527,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                id: \"endDate\",\n                                                type: \"date\",\n                                                value: endDate,\n                                                onChange: (e)=>setEndDate(e.target.value)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                                                lineNumber: 528,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                                        lineNumber: 526,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                                lineNumber: 498,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: handleSearch,\n                                        size: \"sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_MoreHorizontal_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                                                lineNumber: 534,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Search\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                                        lineNumber: 533,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: clearFilters,\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        children: \"Clear Filters\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                                        lineNumber: 537,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: handleExport,\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_MoreHorizontal_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                                                lineNumber: 541,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Export\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                                        lineNumber: 540,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                                lineNumber: 532,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                        lineNumber: 497,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                lineNumber: 493,\n                columnNumber: 7\n            }, this),\n            selectedLeads.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"text-black\",\n                            children: [\n                                \"Bulk Operations (\",\n                                selectedLeads.length,\n                                \" selected)\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                            lineNumber: 551,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                        lineNumber: 550,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handleBulkExport,\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    disabled: bulkLoading,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_MoreHorizontal_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                                            lineNumber: 558,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Export Selected\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                                    lineNumber: 557,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                    onValueChange: handleBulkStatusUpdate,\n                                    disabled: bulkLoading,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                            className: \"w-48\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                placeholder: \"Update Status\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                                                lineNumber: 564,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                                            lineNumber: 563,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                            children: statusOptions.filter((option)=>option.value !== 'all').map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                    value: option.value,\n                                                    children: option.label\n                                                }, option.value, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                                                    lineNumber: 567,\n                                                    columnNumber: 89\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                                            lineNumber: 566,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                                    lineNumber: 562,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handleBulkDelete,\n                                    variant: \"destructive\",\n                                    size: \"sm\",\n                                    disabled: bulkLoading,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_MoreHorizontal_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                                            lineNumber: 574,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Delete Selected\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                                    lineNumber: 573,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: ()=>setSelectedLeads([]),\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    disabled: bulkLoading,\n                                    children: \"Clear Selection\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                                    lineNumber: 578,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                            lineNumber: 556,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                        lineNumber: 555,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                lineNumber: 549,\n                columnNumber: 36\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"text-black\",\n                            children: [\n                                \"Leads (\",\n                                totalCount,\n                                \" total)\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                            lineNumber: 588,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                        lineNumber: 587,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                ...Array(5)\n                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__.Skeleton, {\n                                            className: \"h-12 w-12 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                                            lineNumber: 595,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__.Skeleton, {\n                                                    className: \"h-4 w-[200px]\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                                                    lineNumber: 597,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__.Skeleton, {\n                                                    className: \"h-4 w-[150px]\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                                                    lineNumber: 598,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                                            lineNumber: 596,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, i, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                                    lineNumber: 594,\n                                    columnNumber: 44\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                            lineNumber: 593,\n                            columnNumber: 22\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_data_table__WEBPACK_IMPORTED_MODULE_10__.DataTable, {\n                            columns: columns,\n                            data: leads,\n                            pagination: {\n                                pageIndex: currentPage - 1,\n                                pageSize,\n                                pageCount: totalPages,\n                                total: totalCount,\n                                onPageChange: (page)=>setCurrentPage(page + 1),\n                                onPageSizeChange: setPageSize\n                            }\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                            lineNumber: 601,\n                            columnNumber: 22\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                        lineNumber: 592,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n                lineNumber: 586,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\leads\\\\page.tsx\",\n        lineNumber: 471,\n        columnNumber: 10\n    }, this);\n}\n_s(LeadsPage, \"gNOtrZJPcLYNFiD0i7gsUyIM9r8=\", false, function() {\n    return [\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast\n    ];\n});\n_c1 = LeadsPage;\n_s1(LeadsPage, \"gOPsKH9lLxQrB6AzYPW1EdEZbtw=\", false, function() {\n    return [\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast\n    ];\n});\n_c = LeadsPage;\nvar _c;\n$RefreshReg$(_c, \"LeadsPage\");\nvar _c1;\n$RefreshReg$(_c1, \"LeadsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/leads/page.tsx\n"));

/***/ })

});