'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, BarChart, Bar } from 'recharts';
import { TrendingUp, BarChart3, AlertCircle } from 'lucide-react';
import { ConversionTrends } from '@/lib/validations/conversion.schema';
import { toast } from 'sonner';

interface ConversionTrendsChartProps {
  className?: string;
  showTitle?: boolean;
  defaultPeriod?: 'daily' | 'weekly' | 'monthly' | 'quarterly';
  chartType?: 'line' | 'bar';
}

/**
 * Conversion Trends Chart Component
 * 
 * Displays time-series visualization of conversion trends
 */
export function ConversionTrendsChart({ 
  className = '', 
  showTitle = true,
  defaultPeriod = 'monthly',
  chartType = 'line'
}: ConversionTrendsChartProps) {
  const [trends, setTrends] = useState<ConversionTrends | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [period, setPeriod] = useState<'daily' | 'weekly' | 'monthly' | 'quarterly'>(defaultPeriod);
  const [currentChartType, setCurrentChartType] = useState<'line' | 'bar'>(chartType);

  useEffect(() => {
    fetchTrends();
  }, [period]);

  const fetchTrends = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`/api/conversions/reports/trends?period=${period}`, {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to fetch conversion trends');
      }

      const data = await response.json();
      setTrends(data);
    } catch (error) {
      console.error('Error fetching conversion trends:', error);
      setError('Failed to load conversion trends');
      toast.error('Failed to load conversion trends');
    } finally {
      setIsLoading(false);
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const formatDate = (dateString: string) => {
    switch (period) {
      case 'daily':
        return new Date(dateString).toLocaleDateString('en-IN', { 
          month: 'short', 
          day: 'numeric' 
        });
      case 'weekly':
        return `Week ${dateString.split('W')[1]}`;
      case 'monthly':
        return new Date(dateString + '-01').toLocaleDateString('en-IN', { 
          year: 'numeric', 
          month: 'short' 
        });
      case 'quarterly':
        return dateString;
      default:
        return dateString;
    }
  };

  if (isLoading) {
    return (
      <Card className={className}>
        {showTitle && (
          <CardHeader className="bg-primary text-white">
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Conversion Trends
            </CardTitle>
          </CardHeader>
        )}
        <CardContent className="p-6">
          <Skeleton className="h-80 w-full" />
        </CardContent>
      </Card>
    );
  }

  if (error || !trends) {
    return (
      <Card className={className}>
        {showTitle && (
          <CardHeader className="bg-primary text-white">
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Conversion Trends
            </CardTitle>
          </CardHeader>
        )}
        <CardContent className="p-6">
          <div className="flex items-center gap-2 text-destructive">
            <AlertCircle className="h-5 w-5" />
            <span>{error || 'Failed to load trends'}</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  const chartData = trends.data.map(item => ({
    ...item,
    date: formatDate(item.date),
    formattedRevenue: formatCurrency(item.totalRevenue),
  }));

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium">{label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} style={{ color: entry.color }}>
              {entry.name}: {entry.name.includes('Revenue') ? formatCurrency(entry.value) : entry.value}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  return (
    <Card className={className}>
      {showTitle && (
        <CardHeader className="bg-primary text-white">
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Conversion Trends
            </div>
            <div className="flex items-center gap-2">
              <Select value={currentChartType} onValueChange={(value: 'line' | 'bar') => setCurrentChartType(value)}>
                <SelectTrigger className="w-24 bg-white text-black">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="line">Line</SelectItem>
                  <SelectItem value="bar">Bar</SelectItem>
                </SelectContent>
              </Select>
              <Select value={period} onValueChange={(value: 'daily' | 'weekly' | 'monthly' | 'quarterly') => setPeriod(value)}>
                <SelectTrigger className="w-32 bg-white text-black">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="daily">Daily</SelectItem>
                  <SelectItem value="weekly">Weekly</SelectItem>
                  <SelectItem value="monthly">Monthly</SelectItem>
                  <SelectItem value="quarterly">Quarterly</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardTitle>
        </CardHeader>
      )}
      <CardContent className="p-6">
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            {currentChartType === 'line' ? (
              <LineChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip content={<CustomTooltip />} />
                <Legend />
                <Line 
                  type="monotone" 
                  dataKey="totalConversions" 
                  stroke="#0F52BA" 
                  strokeWidth={2}
                  name="Total Conversions"
                />
                <Line 
                  type="monotone" 
                  dataKey="warrantyToAmc" 
                  stroke="#10B981" 
                  strokeWidth={2}
                  name="Warranty → AMC"
                />
                <Line 
                  type="monotone" 
                  dataKey="amcToOutWarranty" 
                  stroke="#F59E0B" 
                  strokeWidth={2}
                  name="AMC → Out-Warranty"
                />
                <Line 
                  type="monotone" 
                  dataKey="warrantyToOutWarranty" 
                  stroke="#EF4444" 
                  strokeWidth={2}
                  name="Warranty → Out-Warranty"
                />
              </LineChart>
            ) : (
              <BarChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip content={<CustomTooltip />} />
                <Legend />
                <Bar dataKey="warrantyToAmc" fill="#10B981" name="Warranty → AMC" />
                <Bar dataKey="amcToOutWarranty" fill="#F59E0B" name="AMC → Out-Warranty" />
                <Bar dataKey="warrantyToOutWarranty" fill="#EF4444" name="Warranty → Out-Warranty" />
              </BarChart>
            )}
          </ResponsiveContainer>
        </div>

        {/* Summary Statistics */}
        <div className="mt-6 grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-primary">
              {chartData.reduce((sum, item) => sum + item.totalConversions, 0)}
            </div>
            <div className="text-sm text-muted-foreground">Total Conversions</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {chartData.reduce((sum, item) => sum + item.warrantyToAmc, 0)}
            </div>
            <div className="text-sm text-muted-foreground">Warranty → AMC</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-600">
              {chartData.reduce((sum, item) => sum + item.amcToOutWarranty, 0)}
            </div>
            <div className="text-sm text-muted-foreground">AMC → Out-Warranty</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">
              {chartData.reduce((sum, item) => sum + item.warrantyToOutWarranty, 0)}
            </div>
            <div className="text-sm text-muted-foreground">Warranty → Out-Warranty</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
