import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { getSalesOrderRepository } from '@/lib/repositories';
import { updateSalesOrderSchema } from '@/lib/validations/sales.schema';
import { z } from 'zod';

/**
 * GET /api/sales/orders/[id]
 * Get a specific sales order by ID
 */
export const GET = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'],
  async (
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      const { id } = await params;

      const salesOrderRepository = getSalesOrderRepository();
      const salesOrder = await salesOrderRepository.findById(id);

      if (!salesOrder) {
        return NextResponse.json(
          {
            success: false,
            error: 'Sales order not found',
          },
          { status: 404 }
        );
      }

      return NextResponse.json({
        success: true,
        data: salesOrder,
      });
    } catch (error) {
      console.error('Error fetching sales order:', error);
      
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to fetch sales order',
        },
        { status: 500 }
      );
    }
  }
);

/**
 * PUT /api/sales/orders/[id]
 * Update a specific sales order
 */
export const PUT = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  async (
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      const { id } = await params;
      const body = await request.json();

      // Validate request body
      const validatedData = updateSalesOrderSchema.parse({ ...body, id });

      const salesOrderRepository = getSalesOrderRepository();

      // Check if sales order exists
      const existingSalesOrder = await salesOrderRepository.findById(id);
      if (!existingSalesOrder) {
        return NextResponse.json(
          {
            success: false,
            error: 'Sales order not found',
          },
          { status: 404 }
        );
      }

      // Update sales order
      const updateData: any = {};
      
      if (validatedData.customerId) updateData.customerId = validatedData.customerId;
      if (validatedData.executiveId) updateData.executiveId = validatedData.executiveId;
      if (validatedData.orderDate) updateData.orderDate = validatedData.orderDate;
      if (validatedData.contactPerson !== undefined) updateData.contactPerson = validatedData.contactPerson;
      if (validatedData.contactPhone !== undefined) updateData.contactPhone = validatedData.contactPhone;
      if (validatedData.status) updateData.status = validatedData.status;
      if (validatedData.deliveryDate !== undefined) updateData.deliveryDate = validatedData.deliveryDate;
      if (validatedData.actualDeliveryDate !== undefined) updateData.actualDeliveryDate = validatedData.actualDeliveryDate;
      if (validatedData.amount !== undefined) updateData.amount = validatedData.amount;
      if (validatedData.remarks !== undefined) updateData.remarks = validatedData.remarks;

      const updatedSalesOrder = await salesOrderRepository.update(id, updateData);

      return NextResponse.json({
        success: true,
        data: updatedSalesOrder,
        message: 'Sales order updated successfully',
      });
    } catch (error) {
      console.error('Error updating sales order:', error);
      
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid request data',
            details: error.errors,
          },
          { status: 400 }
        );
      }
      
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to update sales order',
        },
        { status: 500 }
      );
    }
  }
);

/**
 * DELETE /api/sales/orders/[id]
 * Delete a specific sales order
 */
export const DELETE = withRoleProtection(
  ['ADMIN', 'MANAGER'],
  async (
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      const { id } = await params;

      const salesOrderRepository = getSalesOrderRepository();

      // Check if sales order exists
      const existingSalesOrder = await salesOrderRepository.findById(id);
      if (!existingSalesOrder) {
        return NextResponse.json(
          {
            success: false,
            error: 'Sales order not found',
          },
          { status: 404 }
        );
      }

      // Delete sales order
      await salesOrderRepository.delete(id);

      return NextResponse.json({
        success: true,
        message: 'Sales order deleted successfully',
      });
    } catch (error) {
      console.error('Error deleting sales order:', error);
      
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to delete sales order',
        },
        { status: 500 }
      );
    }
  }
);
