'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator
} from '@/components/ui/breadcrumb';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { toast } from '@/components/ui/use-toast';
import { Loader2, Home, Upload, Calendar } from 'lucide-react';

// Define the form schema
const visitCardFormSchema = z.object({
  customerId: z.string().optional().refine((val) => {
    if (!val) return false; // Required field
    return z.string().uuid().safeParse(val).success; // Must be valid UUID
  }, { message: 'Please select a customer' }),
  notes: z.string().optional(),
  file: z.any().optional()
});

type VisitCardFormValues = z.infer<typeof visitCardFormSchema>;

/**
 * Create Visit Card Page
 *
 * This page provides a form for creating a new visit card.
 */
export default function CreateVisitCardPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const initialCustomerId = searchParams.get('customerId');

  const [customers, setCustomers] = useState<any[]>([]);
  const [users, setUsers] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  // Initialize form with react-hook-form
  const form = useForm<VisitCardFormValues>({
    resolver: zodResolver(visitCardFormSchema),
    defaultValues: {
      customerId: initialCustomerId || undefined,
      notes: '',
    },
    mode: 'onChange', // Enable validation on change
  });

  // Update form values when initialCustomerId changes
  useEffect(() => {
    if (initialCustomerId) {
      form.setValue('customerId', initialCustomerId, { shouldValidate: true });
    }
  }, [initialCustomerId]); // Removed 'form' to prevent infinite re-renders

  // Fetch customers and users data
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        // Fetch customers
        const customersResponse = await fetch('/api/customers?take=100', {
          credentials: 'include'
        });
        if (customersResponse.ok) {
          const customersData = await customersResponse.json();
          setCustomers(customersData.success ? customersData.data : customersData.customers || []);
        }

        // Fetch users
        const usersResponse = await fetch('/api/users?take=100', {
          credentials: 'include'
        });
        if (usersResponse.ok) {
          const usersData = await usersResponse.json();
          setUsers(usersData.users || []);
        }
      } catch (error) {
        console.error('Error fetching data:', error);
        toast({
          title: 'Error',
          description: 'Failed to load required data. Please try again.',
          variant: 'destructive'
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  // Handle file change
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setSelectedFile(e.target.files[0]);
    }
  };

  // Handle form submission
  const onSubmit = async (data: VisitCardFormValues) => {
    if (!selectedFile) {
      toast({
        title: 'Error',
        description: 'Please select a file to upload',
        variant: 'destructive'
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // First, upload the file
      const formData = new FormData();
      formData.append('file', selectedFile);
      formData.append('customerId', data.customerId);

      const uploadResponse = await fetch('/api/upload/visit-card', {
        method: 'POST',
        body: formData,
        credentials: 'include'
      });

      if (!uploadResponse.ok) {
        throw new Error('Failed to upload file');
      }

      const uploadResult = await uploadResponse.json();

      // Then, create the visit card with the file path
      const visitCardData = {
        customerId: data.customerId,
        notes: data.notes,
        filePath: uploadResult.filePath
      };

      const createResponse = await fetch('/api/visit-cards', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(visitCardData),
        credentials: 'include'
      });

      if (!createResponse.ok) {
        throw new Error('Failed to create visit card');
      }

      toast({
        title: 'Success',
        description: 'Visit card created successfully'
      });

      // Redirect to the visit cards list or the customer's visit cards tab
      if (data.customerId) {
        router.push(`/customers/${data.customerId}?tab=visitCards`);
      } else {
        router.push('/visit-cards');
      }
    } catch (error: any) {
      console.error('Error creating visit card:', error);
      toast({
        title: 'Error',
        description: error.message || 'Failed to create visit card. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">

      <Card>
        <CardHeader className="pb-3 flex flex-row items-center justify-between bg-primary text-white">
          <div>
            <CardTitle>Create New Visit Card</CardTitle>
            <CardDescription className="text-gray-100">
              Upload a new visit card and associate it with a customer
            </CardDescription>
          </div>
          <Button variant="secondary" onClick={() => router.back()}>
            Cancel
          </Button>
        </CardHeader>
        <CardContent className="pt-6">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Customer Selection */}
                <FormField
                  control={form.control}
                  name="customerId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Customer*</FormLabel>
                      <Select
                        disabled={isLoading || isSubmitting}
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a customer" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {customers.map((customer) => (
                            <SelectItem key={customer.id} value={customer.id}>
                              {customer.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Select the customer this visit card is for
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Notes */}
                <FormField
                  control={form.control}
                  name="notes"
                  render={({ field }) => (
                    <FormItem className="md:col-span-2">
                      <FormLabel>Notes</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Additional notes about the visit"
                          disabled={isSubmitting}
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Any additional information about the visit
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* File Upload */}
                <div className="md:col-span-2">
                  <FormLabel htmlFor="file">Visit Card File*</FormLabel>
                  <div className="mt-1 flex items-center">
                    <Input
                      id="file"
                      type="file"
                      accept=".pdf,.jpg,.jpeg,.png"
                      onChange={handleFileChange}
                      disabled={isSubmitting}
                      className="flex-1"
                    />
                  </div>
                  <p className="text-sm text-gray-500 mt-1">
                    Upload the visit card document (PDF, JPG, JPEG, or PNG)
                  </p>
                  {selectedFile && (
                    <p className="text-sm text-green-600 mt-1">
                      Selected file: {selectedFile.name}
                    </p>
                  )}
                </div>
              </div>

              <div className="flex justify-end space-x-4">
                <Button type="submit" disabled={isSubmitting || isLoading}>
                  {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Create Visit Card
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
