/**
 * History Overview Component
 * 
 * This component displays a comprehensive overview of customer history
 * including repairs, maintenance, complaints, warranty, and AMC information.
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from 'sonner';
import {
  CalendarDays,
  Wrench,
  Settings,
  AlertTriangle,
  Shield,
  FileText,
  TrendingUp,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
} from 'lucide-react';
import { CustomerHistoryOverview, HistoryActivitySummary } from '@/types/history.types';

interface HistoryOverviewProps {
  customerId: string;
  className?: string;
}

interface HistoryOverviewData {
  overview: CustomerHistoryOverview;
  statistics?: {
    totalRepairs: number;
    completedRepairs: number;
    pendingRepairs: number;
    averageRepairTime: number;
    totalCost: number;
  };
}

export function HistoryOverview({ customerId, className }: HistoryOverviewProps) {
  const [data, setData] = useState<HistoryOverviewData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchHistoryOverview();
  }, [customerId]);

  const fetchHistoryOverview = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(
        `/api/history/overview?customerId=${customerId}&includeStatistics=true`,
        {
          credentials: 'include',
        }
      );

      if (!response.ok) {
        throw new Error('Failed to fetch history overview');
      }

      const result = await response.json();
      if (result.success) {
        setData(result.data);
      } else {
        throw new Error(result.error || 'Failed to fetch history overview');
      }
    } catch (error) {
      console.error('Error fetching history overview:', error);
      setError(error instanceof Error ? error.message : 'An error occurred');
      toast.error('Failed to load history overview');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'completed':
      case 'resolved':
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'pending':
      case 'scheduled':
        return 'bg-yellow-100 text-yellow-800';
      case 'in_progress':
      case 'in-progress':
        return 'bg-blue-100 text-blue-800';
      case 'cancelled':
      case 'expired':
        return 'bg-gray-100 text-gray-800';
      case 'overdue':
      case 'critical':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityIcon = (priority: string) => {
    switch (priority?.toLowerCase()) {
      case 'critical':
      case 'urgent':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'high':
        return <AlertCircle className="h-4 w-4 text-orange-500" />;
      case 'medium':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'low':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'REPAIR':
        return <Wrench className="h-4 w-4" />;
      case 'MAINTENANCE':
        return <Settings className="h-4 w-4" />;
      case 'COMPLAINT':
        return <AlertTriangle className="h-4 w-4" />;
      case 'WARRANTY':
        return <Shield className="h-4 w-4" />;
      case 'AMC':
        return <FileText className="h-4 w-4" />;
      default:
        return <CalendarDays className="h-4 w-4" />;
    }
  };

  if (loading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="pb-2">
                <Skeleton className="h-4 w-24" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-16" />
              </CardContent>
            </Card>
          ))}
        </div>
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-32" />
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {[...Array(5)].map((_, i) => (
                <Skeleton key={i} className="h-16 w-full" />
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error || !data) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <XCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <p className="text-gray-600 mb-4">{error || 'No data available'}</p>
            <Button onClick={fetchHistoryOverview} variant="outline">
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  const { overview, statistics } = data;

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Repairs</CardTitle>
            <Wrench className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overview.totalRepairs}</div>
            {statistics && (
              <p className="text-xs text-muted-foreground">
                {statistics.completedRepairs} completed
              </p>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Maintenance</CardTitle>
            <Settings className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overview.totalMaintenance}</div>
            {overview.nextMaintenanceDate && (
              <p className="text-xs text-muted-foreground">
                Next: {new Date(overview.nextMaintenanceDate).toLocaleDateString()}
              </p>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Complaints</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overview.totalComplaints}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active AMCs</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overview.activeAMCs}</div>
            <p className="text-xs text-muted-foreground">
              {overview.totalWarrantyClaims} warranty claims
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Information */}
      <Tabs defaultValue="activity" className="space-y-4">
        <TabsList>
          <TabsTrigger value="activity">Recent Activity</TabsTrigger>
          <TabsTrigger value="statistics">Statistics</TabsTrigger>
        </TabsList>

        <TabsContent value="activity" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>
                Latest service activities for {overview.customer.name}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {overview.recentActivity.length === 0 ? (
                <p className="text-center text-gray-500 py-8">No recent activity</p>
              ) : (
                <div className="space-y-4">
                  {overview.recentActivity.map((activity) => (
                    <div
                      key={activity.id}
                      className="flex items-start space-x-4 p-4 border rounded-lg"
                    >
                      <div className="flex-shrink-0">
                        {getActivityIcon(activity.type)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <p className="text-sm font-medium text-gray-900">
                            {activity.description}
                          </p>
                          <div className="flex items-center space-x-2">
                            {activity.priority && getPriorityIcon(activity.priority)}
                            {activity.status && (
                              <Badge className={getStatusColor(activity.status)}>
                                {activity.status}
                              </Badge>
                            )}
                          </div>
                        </div>
                        <div className="flex items-center space-x-4 mt-1">
                          <p className="text-sm text-gray-500">
                            {new Date(activity.date).toLocaleDateString()}
                          </p>
                          <Badge variant="outline">{activity.type}</Badge>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="statistics" className="space-y-4">
          {statistics && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle>Repair Statistics</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between">
                    <span>Total Repairs:</span>
                    <span className="font-semibold">{statistics.totalRepairs}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Completed:</span>
                    <span className="font-semibold text-green-600">
                      {statistics.completedRepairs}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Pending:</span>
                    <span className="font-semibold text-yellow-600">
                      {statistics.pendingRepairs}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Avg. Repair Time:</span>
                    <span className="font-semibold">
                      {statistics.averageRepairTime.toFixed(1)} hours
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Total Cost:</span>
                    <span className="font-semibold">
                      ₹{statistics.totalCost.toLocaleString()}
                    </span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Service Summary</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between">
                    <span>History Cards:</span>
                    <span className="font-semibold">{overview.totalHistoryCards}</span>
                  </div>
                  {overview.lastServiceDate && (
                    <div className="flex justify-between">
                      <span>Last Service:</span>
                      <span className="font-semibold">
                        {new Date(overview.lastServiceDate).toLocaleDateString()}
                      </span>
                    </div>
                  )}
                  {overview.nextMaintenanceDate && (
                    <div className="flex justify-between">
                      <span>Next Maintenance:</span>
                      <span className="font-semibold">
                        {new Date(overview.nextMaintenanceDate).toLocaleDateString()}
                      </span>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
